// 引入路由
const express = require("express");
const multer = require("multer");
const router = express.Router();
// 引入数据库操作方法
const { query, TripQuery, searchQuery } = require("../common/connection");
// 引入sql操作方法
const { fetchFlynit } = require("../common/sql");
// 引入获取ip地址
const { getClientIp, getAgent } = require("../common/ip");
// 引入token验证
const verifyToken = require("../middleware/auth");
// 引入加密模块
const { encryptPassword, passwordHash } = require("../common/md5");
// 引入jwt
const gentoken = require("../token/jwt");
const moment = require("moment");
// 引入redis
const client = require("../common/redis");
const path = require("path");
const fs = require("fs");
// 填入joi验证模块
const Joi = require("joi");
// 引入excel模块
const xlsx = require("xlsx");
const { v4: uuidv4 } = require("uuid");
// 引入qrcode
const QRCode = require("qrcode");
// 引入发送邮件的方法
const { getEmail } = require("../common/email");
//import Notice from '../views/notice.html'; // 确保路径正确

const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, "uploads/");
  },
  filename: function (req, file, cb) {
    // 判断文件类型，这里将原始文件的扩展名添加到文件名中，以防止文件名冲突
    if (
      file.mimetype === "image/jpeg" ||
      file.mimetype === "image/png" ||
      file.mimetype === "image/jpg"
    ) {
      cb(null, "tzml" + Date.now() + path.extname(file.originalname));
    }
    if (
      file.mimetype ===
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    ) {
      // 保存原文件名
      cb(null, file.originalname);
    }
  },
});

const upload = multer({ storage: storage });

// 登录接口
// 登录接口
router.post("/v1/user_login", async (req, res) => {
  try {
    const { uuid, password } = req.body;
    // 验证参数
    if (!uuid && !password) {
      return res.Error("参数验证失败", 10001);
    }
    // 查询当前用户是否存在
    const result = await query("select * from users where uuid = ?", [uuid]);
    if (result.length === 0) {
      return res.Error("账户不存在，请联系管理员", 10003);
    }
    // 验证是否是管理员
    if (result[0].level === 1) {
      return res.Error("仅限员工账号登录", 10008);
    }
    // 验证用户是否被禁用
    if (result[0].is_ban === 1) {
      return res.Error("您的账户已被禁用", 30000);
    }
    // 验证密码是否正确
    if (result[0].password !== encryptPassword(password)) {
      return res.Error("密码错误");
    }
    // 定义生成token的数据,简短token长度
    const obj = {
      userId: result[0].uuid,
      username: result[0].username,
    };
    // 生成token
    const token = gentoken(obj);
    
    try {
      // 将token存入redis，使用新版本的 Redis 命令
      await client.set(`userToken:${uuid}`, token, {
        EX: 43200 // 设置过期时间为12小时
      });
      
      // 更新token
      await query("update users set token = ? where uuid = ?", [token, uuid]);
      
      // 更新登录状态
      await query("update users set is_online = 1 where uuid = ?", [uuid]);
      
      // 将用户信息存入redis，使用新版本的 Redis 命令
      await client.set(`userInfo:${uuid}`, JSON.stringify(result[0]), {
        EX: 604800 // 设置过期时间为7天
      });
      
      // 将token加入到返回数据中
      result[0].token = token;
      // 删除密码
      delete result[0].password;
      
      // 返回数据
      return res.Success("登录成功", { list: result[0] });
    } catch (redisError) {
      console.error('Redis Error:', redisError);
      return res.Error("登录失败：Redis 存储错误");
    }
  } catch (error) {
    console.error('Login Error:', error);
    return res.Error("服务器异常: " + error.message, 500);
  }
});

// 退出登录接口
router.post("/v1/user_logout", verifyToken, async (req, res) => {
  try {
    const { user } = req;
    const uuid = user.userId;
    // 更改users表中的登录状态
    await query("update users set is_online = 0 where uuid = ?", [uuid]);
    // 清除redis中的token
    client.del(`userToken:${uuid}`);
    // 清除redis中的用户信息
    client.del(`userInfo:${uuid}`);
    // 清除打卡信息
    client.del(`is_daka:${uuid}:${uuid}`);
    // 返回数据
    res.Success("退出成功");
  } catch (error) {
    return res.Error("服务器异常", 500);
  }
});

// 获取同部门下的用户列表
router.get("/v1/getUserListByDepartment", verifyToken, async (req, res) => {
  try {
    const { user } = req;
    const uuid = user.userId;
    // 根据uuid查询redis中的用户信息
    const userInfo = await client.get(`userInfo:${uuid}`);
    // 获取当前用户的部门
    const department = JSON.parse(userInfo).department;
    // 查询当前部门下的用户列表
    const result = await query(
      "select username, uuid, shift, department, is_online from users where department = ? AND level = ? ",
      [department, 0]
    );
    if (result.length === 0) {
      return res.Error("获取失败");
    } else {
      // 返回数据
      res.Success("获取成功", { list: result });
    }
  } catch (error) {
    return res.Error("服务器异常", 500);
  }
});

// 封装发送邮件的方法
async function sendEmailsToMultipleRecipients(recipients, msg) {
  const results = [];
  for (const mail of recipients) {
    const result = await getEmail(mail, msg);
    results.push(result);
  }
  return results;
}

// 添加一个简单的路由
async function sendTestEmails(req, res) {
  const { user } = req;
  const uuid = user.userId;
  // 发送邮件给多个领导
  const recipients = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
  ];
  const msg = "IT";
  const results = await sendEmailsToMultipleRecipients(recipients, msg);
  // 返回数据
  res.Success("邮件发送成功", { list: results });
}

// 测试邮件发送接口
router.get("/v1/test", verifyToken, sendTestEmails);

// 加载邮件模板html文件
router.get("/", async (req, res) => {
  res.sendFile(path.join(__dirname, "../views/index.html"));
});

// 后台管理系统添加用户接口
router.post("/v1/addUser", verifyToken, async (req, res) => {
  try {
    const { username, password, sex, department, uuid, shift, avatar, type } =
      req.body;
    // 验证参数
    if (
      !username ||
      !password ||
      !department ||
      !uuid ||
      !shift ||
      !avatar ||
      !sex
    ) {
      return res.Error("参数验证失败", 10001);
    }
    const { user } = req;
    const uid = user.userId;
    // 根据uuid获取redis用户信息
    const userInfo = await client.get(`userInfo:${uid}`);
    // 获取当前用户的等级
    const level = JSON.parse(userInfo).level;
    // 判断权限
    if (level !== 1) {
      return res.Error("您没有权限操作!");
    }
    // 获取当前时间
    const time = moment().format("YYYY-MM-DD HH:mm:ss");
    // 将密码进行加密
    const newPass = encryptPassword(password);
    // 如果isAdd为true,则为添加用户,否则为修改用户
    if (type) {
      // 查询当前用户是否存在
      const result = await query("select * from users where uuid = ?", [uuid]);
      if (result.length > 0) {
        return res.Error("当前工号的用户已存在");
      }
      // 添加用户
      const addResult = await query(
        "insert into users (username, password, uuid, sex, shift, department, avatar, create_time) values (?, ?, ?, ?, ?, ?, ?, ?)",
        [username, newPass, uuid, sex, shift, department, avatar, time]
      );
      if (addResult.affectedRows === 1) {
        return res.Success("添加成功");
      }
      return res.Error("添加失败");
    } else {
      // 查询当前用户是否存在
      const result = await query("select * from users where uuid = ?", [uuid]);
      if (result.length === 0) {
        return res.Error("当前用户不存在");
      }
      // 修改用户
      const modifyUser = await query(
        "update users set username = ?, password = ?, sex = ?, shift = ?, department = ?, avatar = ?  where uuid = ?",
        [username, newPass, sex, shift, department, avatar, uuid]
      );
      if (modifyUser.affectedRows === 1) {
        return res.Success("修改成功");
      }

      return res.Error("修改失败");
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理系统获取用户列表接口
router.get("/v1/getUserList", async (req, res) => {
  try {
    // 获取当前页码
    const { page, pageSize } = req.query;
    // 验证参数
    const validatedPage = page ? parseInt(page) : 1;
    const validatedPageSize = pageSize ? parseInt(pageSize) : 10;
    // 查询用户列表
    const result = await query("select * from users limit ?,?", [
      (validatedPage - 1) * validatedPageSize,
      validatedPageSize,
    ]);
    // 查询用户总数
    const count = await query("select count(*) as count from users");
    if (result.length === 0 || count.length === 0) {
      return res.Error("获取失败");
    }
    // 返回数据
    res.Success("获取成功", { list: result, count: count[0].count });
  } catch (error) {
    return res.Error("服务器异常", 500);
  }
});

// 后台管理系统添加工作项接口
router.post("/v1/addWorkItem", verifyToken, async (req, res) => {
  try {
    const { workItem, workNum, unit, depart, types, idd } = req.body;
    // 验证参数
    if (!workItem && !workNum && !unit && !department) {
      return res.Error("参数验证失败", 10001);
    }
    // 获取当前时间
    const time = moment().format("YYYY-MM-DD HH:mm:ss");
    // 获取req中的用户id
    const { user } = req;
    const userId = user.userId;
    // 如果types为add,则为添加工作项,否则为修改工作项
    if (types === "add") {
      // 查询当前工作项是否存在
      const results = await query("select * from worklist where content = ?", [
        workItem,
      ]);
      if (results.length > 0) {
        return res.Error("当前工作项已存在");
      }
      // 添加工作项
      const result = await TripQuery(
        "insert into worklist (content, work_num, unit ,department, user_id, create_time) values (?,?,?,?,?,?)",
        [workItem, workNum, unit, depart, userId, time]
      );
      if (result.affectedRows === 1) {
        res.Success("添加成功");
      } else {
        return res.Error("添加失败");
      }
    } else {
      // 修改工作项
      const result = await query(
        "update worklist set content = ?, work_num = ?, unit = ?, department = ? where id = ?",
        [workItem, workNum, unit, depart, idd]
      );
      if (result.affectedRows === 1) {
        res.Success("修改成功");
      } else {
        return res.Error("修改失败");
      }
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 加油打卡接口
router.post("/v1/addOil", verifyToken, async (req, res) => {
  try {
    const { oil_areas, oil_time, consume } = req.body;
    // 验证参数
    if (!oil_areas && !oil_time && !consume) {
      return res.Error("参数验证失败", 10001);
    }
    const { user } = req;
    const idcard = user.userId;
    const uuid = user.userId;
    // 根据idcard获取redis信息
    const userInfo = await client.get(`userInfo:${idcard}`);
    const department = JSON.parse(userInfo).department;
    // 如果已经打过卡了，就不能再打卡了
    const is_daka = await client.get(`is_daka:${idcard}:${uuid}`);
    if (is_daka) {
      return res.Error("今天已经打过卡了", 10002);
    }
    // 获取当前时间
    const time = moment().format("YYYY-MM-DD HH:mm:ss");
    // 添加工作项
    const result = await query(
      "insert into brush_oil (idcard, department, location, brush_time, create_time, consume_time) values (?,?,?,?,?,?)",
      [idcard, department, oil_areas, oil_time, time, consume]
    );
    // 设置redis缓存
    const daka = 1;
    const key = `is_daka:${idcard}:${uuid}`;
    // client.set(key, daka, "NX", "EX", 8 * 60 * 60);
    client.set(key, daka, 8 * 60 * 60);
    if (result.affectedRows === 1) {
      return res.Success("打卡成功");
    } else {
      return res.Error("打卡失败");
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 获取加油记录
router.get("/v1/getOilList", verifyToken, async (req, res) => {
  try {
    const { user } = req;
    const idcard = user.userId;
    // 根据idcard查询本月内的加油记录，时间字段是datetime 类型
    const result = await query(
      'select * from brush_oil where idcard = ? and DATE_FORMAT(create_time, "%Y-%m") = DATE_FORMAT(CURDATE(), "%Y-%m")',
      [idcard]
    );
    if (result.length === 0) {
      return res.Success("获取成功", { list: [] });
    }
    // 返回数据
    res.Success("获取成功", { list: result });
  } catch (error) {
    return res.Error("服务器异常", 500);
  }
});

// 获取工作内容列表
router.get("/v1/getDataList", verifyToken, async (req, res) => {
  try {
    // 获取用户的部门
    const { user } = req;
    const uid = user.userId;
    const userInfo = await client.get(`userInfo:${uid}`);
    const department = JSON.parse(userInfo).department;
    // 获取内容列表
    const result = await query("select * from worklist where department = ?", [
      department,
    ]);
    // 返回数据
    res.Success("获取成功", { list: result });
  } catch (error) {
    return res.Error("服务器异常", 500);
  }
});

// 存储保全日工作内容
router.post("/v1/addWorkContent", verifyToken, async (req, res) => {
  try {
    const { workdata } = req.body;
    const { user } = req;
    const uid = user.userId;
    // 验证参数
    if (!workdata) {
      return res.Error("参数验证失败", 10001);
    }
    const data = JSON.parse(workdata);
    const machineList = data[0].machineGroup.join(",");
    const time = (new Date().getTime() / 1000).toFixed(0);
    const date = moment(Number(time) * 1000).format("YYYY-MM-DD HH:mm:ss");
    const userInfo = await client.get(`userInfo:${uid}`);
    const shift = JSON.parse(userInfo).shift;

    let totalScore = 0;

    // 根据当前时间判断时间范围
    const currentTime = moment().format("HH:mm:ss");
    let startTime = "";
    let endTime = "";
    if (currentTime >= "07:30:00" && currentTime < "19:30:00") {
      startTime = moment().format("YYYY-MM-DD") + " 07:30:00";
      endTime = moment().format("YYYY-MM-DD") + " 19:30:00";
    } else {
      startTime =
        moment().subtract(1, "days").format("YYYY-MM-DD") + " 19:30:00";
      endTime = moment().format("YYYY-MM-DD") + " 07:30:00";
    }
    // 根据uuid和时间范围查询efficiency表是否有记录
    const efficiencyResult = await TripQuery(
      "SELECT order_id, score FROM efficiency WHERE uuid = ? AND work_day >= ? AND work_day <= ?",
      [uid, startTime, endTime]
    );
    let oid = "";
    // 获取数据库里的score
    let scores = 0;
    if (efficiencyResult.length > 0) {
      oid = efficiencyResult[0].order_id;
      scores = efficiencyResult[0].score;
    } else {
      oid =
        moment().format("YYYYMMDDHHmmss") + Math.floor(Math.random() * 10000);
      await TripQuery(
        "INSERT INTO efficiency (order_id, uuid, shift, score, work_day, create_time) VALUES (?, ?, ?, ?, ?, ?)",
        [oid, uid, shift, 0, date, time]
      );
    }

    const promises = data.map(async (item) => {
      const { content, work_num, department, val, machineGroup } = item;
      const score = (val / work_num).toFixed(2);
      totalScore += Number(score);

      const result = await TripQuery(
        "INSERT INTO dailywork (uuid, shift, content, quantity, depart, work_num, deviceList, shift_score, create_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
        [
          uid,
          shift,
          content,
          val,
          department,
          work_num,
          machineList,
          score,
          date,
        ]
      );

      if (result.affectedRows <= 0) {
        throw new Error("插入dailywork表失败");
      }
    });

    await Promise.all(promises);
    // 将总分进行格式化，最多保留两位小数
    totalScore = (totalScore + scores).toFixed(2);
    const result = await TripQuery(
      "UPDATE efficiency SET score = ? WHERE order_id = ?",
      [totalScore, oid]
    );

    if (result.affectedRows > 0) {
      return res.Success("保存成功");
    } else {
      return res.Error("保存失败");
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 获取数据分析，昨日工作量和今日工作量
router.get("/v1/getWorkData", verifyToken, async (req, res) => {
  try {
    const { user } = req;
    const idcard = user.userId;
    // 获取当前时间
    const time = (new Date().getTime() / 1000).toFixed(0);
    // 获取昨日工作量
    // 获取昨日的起始时间
    const yesterdayStart = moment(Number(time) * 1000)
      .subtract(1, "days")
      .startOf("day")
      .format("YYYY-MM-DD HH:mm:ss");
    // 获取昨日的结束时间
    const yesterdayEnd = moment(Number(time) * 1000)
      .subtract(1, "days")
      .endOf("day")
      .format("YYYY-MM-DD HH:mm:ss");
    const yesterdayResult = await query(
      "select sum(quantity) from dailywork where uuid = ? and create_time >= ? and create_time <= ?",
      [idcard, yesterdayStart, yesterdayEnd]
    );
    // 获取今日工作量
    // 获取今日的起始时间
    const todayStart = moment(Number(time) * 1000)
      .startOf("day")
      .format("YYYY-MM-DD HH:mm:ss");
    // 获取今日的结束时间
    const todayEnd = moment(Number(time) * 1000)
      .endOf("day")
      .format("YYYY-MM-DD HH:mm:ss");
    const todayResult = await query(
      "select sum(quantity) from dailywork where uuid = ? and create_time >= ? and create_time <= ?",
      [idcard, todayStart, todayEnd]
    );
    // 获取今日的工作效率得分
    const efficiencyResult = await query(
      "select score from efficiency where uuid = ? and work_day >= ? and work_day <=?",
      [idcard, todayStart, todayEnd]
    );
    // 获取从weekStart到weekEnd中间的每一天的日期
    let weekDays = [];
    function getDatesBetween() {
      const today = new Date();
      const startOfWeek = new Date(today);
      startOfWeek.setDate(today.getDate() - today.getDay() + 1); // 1 表示星期一

      const weekDates = [];
      for (let i = 0; i < 7; i++) {
        const date = new Date(startOfWeek);
        date.setDate(startOfWeek.getDate() + i);
        const year = date.getFullYear();
        const month = date.getMonth() + 1; // 月份从 0 开始，所以需要加 1
        const day = date.getDate();

        // 格式化日期，保证月份和日期为两位数
        const formattedMonth = month < 10 ? `0${month}` : month;
        const formattedDay = day < 10 ? `0${day}` : day;

        const formattedDate = `${year}-${formattedMonth}-${formattedDay}`;
        weekDates.push(formattedDate);
      }

      return weekDates;
    }
    weekDays = getDatesBetween();
    // 获取weekDays中每一天的开始时间和结束时间
    let week = [];
    for (let i = 0; i < weekDays.length; i++) {
      const start = moment(weekDays[i])
        .startOf("day")
        .format("YYYY-MM-DD HH:mm:ss");
      const end = moment(weekDays[i])
        .endOf("day")
        .format("YYYY-MM-DD HH:mm:ss");
      week.push({
        start,
        end,
      });
    }
    // 定义一个数组存储每一天的数据
    let weekData = [];
    // 根据uuid和week中的每一天的开始时间和结束时间查询每一天的数据
    for (let i = 0; i < week.length; i++) {
      const result = await query(
        "select sum(quantity) from dailywork where uuid = ? and create_time >= ? and create_time <= ?",
        [idcard, week[i].start, week[i].end]
      );
      // 如果查询到的数据为null,则将数据设置为0
      if (result[0]["sum(quantity)"] === null) {
        result[0]["sum(quantity)"] = 0;
      }
      weekData.push(result[0]["sum(quantity)"]);
    }

    // 获取一周内每日的工作效率得分,没有的默认为0,生成一个数组类似[0,0,0,0,0,0,0]
    const efficiencyWeek = [];
    for (let i = 0; i < week.length; i++) {
      const result = await query(
        "select * from efficiency where uuid = ? and work_day >= ? and work_day <= ?",
        [idcard, week[i].start, week[i].end]
      );
      if (result.length === 0) {
        efficiencyWeek.push(0);
      }
      if (result.length > 0) {
        efficiencyWeek.push(result[0].score);
      }
    }
    // 获取今日工作数据列表
    const todayList = await query(
      "select * from dailywork where uuid = ? and create_time >= ? and create_time <= ?",
      [idcard, todayStart, todayEnd]
    );
    // 将数组反转
    const weekResult = weekData;
    const efficiencyWeekResult = efficiencyWeek;
    // 合并数据
    const result = {
      yesterday: yesterdayResult[0]["sum(quantity)"]
        ? yesterdayResult[0]["sum(quantity)"]
        : 0,
      today: todayResult[0]["sum(quantity)"]
        ? todayResult[0]["sum(quantity)"]
        : 0,
      efficiency: efficiencyResult.length > 0 ? efficiencyResult[0].score : 0,
      week: weekResult,
      efficiencyWeek: efficiencyWeekResult,
      todayList,
    };
    // 返回数据
    res.Success("获取成功", { list: result });
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 获取设备列表
router.get("/v1/getDeviceList", verifyToken, async (req, res) => {
  try {
    const { user } = req;
    const uuid = user.userId;
    // 根据uuid获取redis信息
    const userInfo = await client.get(`userInfo:${uuid}`);
    const department = JSON.parse(userInfo).department;
    // const result = await searchQuery('select area, machine_number from machine_area where department = ?', [department]);
    //const result = await searchQuery('select area, machine_number from machine_area where department = ? limit ?, ?', [department, (page - 1) * pageSize, pageSize]);
    const result = await query(
      "select * from dispatch_area where department = ?",
      [department]
    );
    const list = [];

    for (let i = 0; i < result.length; i++) {
      const { area, machine_range, uuid } = result[i];
      const machineRanges = machine_range.split(",");

      list.push({
        area: area,
        uuid: uuid,
        deviceList: machineRanges,
      });
    }
    // 如果查询到的数据不为空,则返回数据
    if (result.length > 0) {
      return res.Success("获取成功", { list });
    } else {
      return res.Error("获取失败");
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台添加保养分类接口
router.post("/v1/addMaintainType", verifyToken, async (req, res) => {
  try {
    const { type } = req.body;
    const { user } = req;
    const uuid = user.userId;
    // 根据uuid获取redis信息
    const userInfo = await client.get(`userInfo:${uuid}`);
    const department = JSON.parse(userInfo).department;
    const level = JSON.parse(userInfo).level;
    // 检查用户权限
    if (level !== 1) {
      return res.Error("权限不足", 10004);
    }
    // 检查该分类是否已经存在
    const results = await query(
      "select * from classify where classify_name = ? and department = ?",
      [type, department]
    );
    if (results.length > 0) {
      return res.Error("该分类已存在");
    }
    // 如果不存在,则添加
    const result = await query(
      "insert into classify (classify_name, department) values (?, ?)",
      [type, department]
    );
    if (result.affectedRows === 1) {
      return res.Success("添加成功");
    }
    return res.Error("添加失败");
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台根据分类添加保养项目接口
router.post("/v1/addMaintainItem", verifyToken, async (req, res) => {
  try {
    const {
      name,
      describe,
      department,
      classify,
      cycle,
      points,
      equipment,
      types,
      mid,
    } = req.body;
    const { user } = req;
    const uuid = user.userId;
    // 根据uuid获取redis信息
    const userInfo = await client.get(`userInfo:${uuid}`);
    const level = JSON.parse(userInfo).level;
    // 检查用户权限
    if (level !== 1) {
      return res.Error("你的权限不足以操作", 10004);
    }
    // 如果types等于edit,则是编辑操作，否则是添加操作
    if (types === "add") {
      // 检查该内容是否已经存在
      const results = await query(
        "select * from maintaince_list where maintaince_name = ? and department = ?",
        [describe, department]
      );
      if (results.length > 0) {
        return res.Error("该该保养项目已存在");
      }
      // 如果不存在,则添加
      const result = await query(
        "insert into maintaince_list (type, maintaince_name, department, classify_id, cycle, points, equipment_type) values (?, ?, ?, ?, ?, ?, ?)",
        [name, describe, department, classify, cycle, points, equipment]
      );
      if (result.affectedRows === 1) {
        return res.Success("添加成功");
      }
      return res.Error("添加失败");
    } else {
      const classify_id =
        classify === "日保养"
          ? 1
          : classify === "月保养"
          ? 2
          : classify === "季度保养"
          ? 3
          : classify === "半年保养"
          ? 4
          : classify === "年保养"
          ? 5
          : undefined;
      // 检查该内容是否存在
      const results = await query(
        "select * from maintaince_list where department = ? and id = ?",
        [department, mid]
      );
      if (results.length === 0) {
        return res.Error("该该保养项目不存在");
      }
      // 修改操作
      const result = await query(
        "update maintaince_list set type = ?, maintaince_name = ?, department = ?, classify_id = ?, cycle = ?, points = ?, equipment_type = ? where id = ?",
        [name, describe, department, classify_id, cycle, points, equipment, mid]
      );
      if (result.affectedRows === 1) {
        return res.Success("编辑成功");
      }
      return res.Error("编辑失败");
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 前端根据分类获取保养项目接口
router.get("/v1/getMaintainItem", verifyToken, async (req, res) => {
  try {
    const { classify_id } = req.query;
    const { user } = req;
    const uuid = user.userId;
    const userInfo = await client.get(`userInfo:${uuid}`);
    const department = JSON.parse(userInfo).department;
    // 根据分类id获取保养项目
    const result = await query(
      "select * from maintaince_list where classify_id = ? and department = ?",
      [classify_id, department]
    );
    // 如果查询到的数据为空,则返回空数组
    if (result.length === 0) {
      return res.Success("获取成功", { list: [] });
    }
    // 如果查询到的数据不为空,则返回数据
    if (result.length > 0) {
      return res.Success("获取成功", { list: result });
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 统一上传图片接口
router.post(
  "/v1/upload",
  verifyToken,
  upload.single("files"),
  async (req, res) => {
    try {
      const { user } = req;
      const uuid = user.userId;
      // 获取图片
      const { file } = req;
      // 如果没有图片,则返回错误
      if (!file) {
        return res.Error("请选择图片");
      }
      // 获取图片类型
      const fileType = file.mimetype.split("/")[1];
      // 获取图片大小
      const fileSize = file.size;
      // 获取图片路径
      const filePath = file.path;
      // 限制图片大小
      if (fileSize > 1024 * 1024 * 2) {
        return res.Error("图片大小不能超过2M");
      }
      // 限制图片类型
      if (fileType !== "jpg" && fileType !== "jpeg" && fileType !== "png") {
        return res.Error("图片类型只能是jpg,jpeg,png");
      }
      // 图片地址\\转换成/
      let filePaths = path.join(filePath).replace(/\\/g, "/");
      // 上传成功后,将图片路径存入数据库
      const result = await query(
        "insert into images (img_url, uuid) values(?, ?)",
        [filePaths, uuid]
      );
      if (result.affectedRows === 1) {
        return res.Success("上传成功", { url: filePaths });
      }
      return res.Error("上传失败");
    } catch (error) {
      return res.Error("服务器异常" + error.message, 500);
    }
  }
);

// 访问图片接口
router.get("/v1/uploads/:imgUrl", (req, res) => {
  const { imgUrl } = req.params;
  const filePath = path.resolve(__dirname, "../uploads/" + imgUrl);
  fs.access(filePath, fs.constants.F_OK, (error) => {
    if (error) {
      res.status(404).send("文件不存在");
    } else {
      const file = fs.readFileSync(filePath);
      res.writeHead(200, { "Content-Type": "image/jpeg" });
      res.end(file);
    }
  });
});

// 根据设备编号获取设备信息接口
router.get("/v1/getDevicesInfo", verifyToken, async (req, res) => {
  try {
    const { device_id } = req.query;
    // 验证参数
    if (!device_id) {
      return res.Error("参数验证失败", 10001);
    }
    // 获取用户信息
    const { user } = req;
    const uuid = user.userId;
    const userInfo = await client.get(`userInfo:${uuid}`);
    const department = JSON.parse(userInfo).department;
    // 根据设备编号获取设备信息
    const result = await searchQuery(
      "select * from machine_area where machine_number = ? and department = ?",
      [device_id, department]
    );
    // 返回数据
    if (result.length > 0) {
      return res.Success("获取成功", { list: result });
    } else {
      return res.Success("获取成功", { list: [] });
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 提交保养记录接口
router.post("/v1/submitMaintainRecord", verifyToken, async (req, res) => {
  try {
    const {
      type,
      device_id,
      location,
      status,
      remark,
      imgList,
      content,
      start,
      end,
    } = req.body;
    // 验证参数
    if (
      !type ||
      !device_id ||
      !location ||
      !status ||
      !content ||
      !start ||
      !end
    ) {
      return res.Error("参数验证失败", 10001);
    }
    // 获取用户信息
    const { user } = req;
    const uuid = user.userId;
    const userInfo = await client.get(`userInfo:${uuid}`);
    const department = JSON.parse(userInfo).department;
    const shift = JSON.parse(userInfo).shift;
    // 根据uuid获取机台区域
    const machineArea = await query(
      "select * from dispatch_area where uuid = ?",
      [uuid]
    );
    const machine_range = machineArea[0].machine_range;
    // 将machine_range转换成数组
    const machineRangeArr = machine_range.split(",");
    // 判断设备编号是否在机台区域内
    if (machineRangeArr.indexOf(device_id) === -1) {
      return res.Error("该设备不在您的维护区域内", 20000);
    }
    // 根据设备编号和班次检测该设备在该班次是否已经保养过
    const checkResult = await query(
      "select * from maintaince where device_number = ? and shift = ? and department = ? and type = ? and uuid = ?",
      [device_id, shift, department, type, uuid]
    );
    // 如果保养记录不为空,则返回错误
    if (checkResult.length > 0) {
      return res.Error("请勿重复保养", 10006);
    }
    // 统计设备信息,device_id,location,status,department插入到deviceList表中
    // 根据设备编号和部门查询该设备信息是否存在
    const deviceListResult = await query(
      "select * from devicelist where device_number = ? and department = ?",
      [device_id, department]
    );
    let deviceStatus = null;
    if (status == "normal") {
      deviceStatus = 0;
    }
    if (status == "stop") {
      deviceStatus = 1;
    }
    if (status == "broken") {
      deviceStatus = 2;
    }
    if (status == "spare") {
      deviceStatus = 3;
    }
    // 如果设备信息不存在,则插入
    if (deviceListResult.length === 0) {
      await query(
        "insert into devicelist (device_number, location, status, department) values (?, ?, ?, ?)",
        [device_id, location, deviceStatus, department]
      );
    }
    // 如果设备信息存在,则更新
    if (deviceListResult.length > 0) {
      await query(
        "update devicelist set location = ?, status = ? where device_number = ? and department = ?",
        [location, deviceStatus, device_id, department]
      );
    }
    // 获取当前时间
    const create_time = moment().format("YYYY-MM-DD HH:mm:ss");
    // 根据end - satrt计算保养耗时
    const spend_times = moment(end).diff(moment(start), "minutes");
    let spends = null;
    // 如果保养耗时小于0,spent_times为1
    if (spend_times < 1) {
      spends = 1;
    } else {
      spends = spend_times;
    }
    // 提交保养记录
    const result = await query(
      "insert into maintaince (type, device_number, location, remarks, img_url, maintaince_list, department, uuid, spend, start_time, end_time, create_time, shift) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
      [
        type,
        device_id,
        location,
        remark,
        imgList,
        content,
        department,
        uuid,
        spends,
        start,
        end,
        create_time,
        shift,
      ]
    );
    if (result.affectedRows === 1) {
      return res.Success("提交成功");
    }
    return res.Error("提交失败");
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台机修保养区域划分
router.post("/v1/areaDivision", verifyToken, async (req, res) => {
  try {
    const { uuid, area, machine_range, depart, eid } = req.body;
    // 验证参数
    if (!area || !machine_range || !depart) {
      return res.Error("参数验证失败", 10001);
    }
    // 如果type为add,则新增,否则修改
    if (!eid || eid === 0) {
      // 查询该区域是否已经存在
      const checkResult = await query(
        "select * from dispatch_area where area = ? and department = ?",
        [area, depart]
      );
      if (checkResult.length > 0) {
        return res.Error("该区域已经存在", 10006);
      }
      const result = await query(
        "insert into dispatch_area (uuid, area, machine_range, department) values (?, ?, ?, ?)",
        [uuid, area, machine_range, depart]
      );
      if (result.affectedRows === 1) {
        return res.Success("保存成功");
      }
      return res.Error("保存失败");
    }
    if (eid > 0) {
      // 根据eid查询该区域是否存在
      const checkResult = await query(
        "select * from dispatch_area where id = ?",
        [eid]
      );
      if (checkResult.length === 0) {
        return res.Error("该区域不存在");
      }
      // 修改用户
      const result = await query(
        "update dispatch_area set area = ?, machine_range = ?, uuid = ?, department = ? where id = ?",
        [area, machine_range, uuid, depart, eid]
      );
      if (result.affectedRows === 1) {
        return res.Success("修改成功");
      }
      return res.Error("修改失败");
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 根据机修id和部门获取机修区域和根据区域获取机台数量
router.get("/v1/getAreaAndMachine", verifyToken, async (req, res) => {
  try {
    const { type } = req.query;
    const { user } = req;
    const uuid = user.userId;
    const userInfo = await client.get(`userInfo:${uuid}`);
    const department = JSON.parse(userInfo).department;
    const shift = JSON.parse(userInfo).shift;
    // let area = ''
    // 根据uuid和部门获取机修区域
    const areaResult = await query(
      "select * from dispatch_area where uuid = ? and department = ?",
      [uuid, department]
    );
    // if (areaResult.length > 0) {
    //     area = areaResult[0].area
    // }
    // 根据获取到的机修区域获取机台数量
    // const machineResult = await query('select * from machine_area where area = ? and department = ?', [area, department]);
    // 获取今日内当前班次的机修保养记录
    // const maintainceResult = await query('select * from maintaince where type = ? and department = ? and shift = ? and create_time like ?', [department, shift, moment().format('YYYY-MM-DD') + '%']);
    // 根据部门、班次、类型获取本月内的机修保养记录
    const maintainceResult = await query(
      "select * from maintaince where type = ? and uuid = ? and shift = ? and create_time like ?",
      [type, uuid, shift, moment().format("YYYY-MM") + "%"]
    );
    // 根据uuid、department、shift、type获取最大一个id的机修保养记录里面的device_number
    const deviceResult = await query(
      "select * from maintaince where uuid = ? and department = ? and shift = ? and type = ? order by id desc limit 1",
      [uuid, department, shift, type]
    );
    let device_number = "";
    if (deviceResult.length > 0) {
      device_number = deviceResult[0].device_number;
    }
    let str = areaResult[0].machine_range;
    let arr = str.split(",");
    // 定义返回数据
    const data = {
      machine: arr.length,
      maintaince: maintainceResult.length,
      device_number: device_number ? device_number : 0,
    };
    return res.Success("获取成功", { list: data });
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 获取区域列表
router.get("/v1/getAreaList", verifyToken, async (req, res) => {
  try {
    const { user } = req;
    const uuid = user.userId;
    const userInfo = await client.get(`userInfo:${uuid}`);
    const department = JSON.parse(userInfo).department;
    // 根据部门获取区域列表
    const result = await query(
      "select * from dispatch_area where department = ?",
      [department]
    );
    if (result.length > 0) {
      return res.Success("获取成功", { list: result });
    } else {
      return res.Success("获取成功", { list: [] });
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 根据id获取加油详情
router.get("/v1/getOilDetail", verifyToken, async (req, res) => {
  try {
    const { id } = req.query;
    // 根据id获取加油详情
    const result = await query("select * from brush_oil where id = ?", [id]);
    let area = "";
    if (result.length > 0) {
      area = result[0].location;
    }
    // 根据区域获取机台数量
    const machineResult = await query(
      "select * from dispatch_area where area = ?",
      [area]
    );
    if (machineResult.length > 0) {
      // 将result中的consume_time加入到machineResult中
      machineResult[0].consume_time = result[0].consume_time;
      return res.Success("获取成功", { list: machineResult });
    } else {
      return res.Success("获取成功", { list: [] });
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 根据uuid获取个人今日工作进度详情
router.get("/v1/getDailyWrokInfo", verifyToken, async (req, res) => {
  try {
    const { uuid } = req.query;
    // 获取用户信息
    const userInfo = await query("select * from users where uuid = ?", [uuid]);
    const department = userInfo[0].department;
    // 根据uuid获取当前班次加油记录
    const oilResult = await query(
      "select * from brush_oil where idcard = ? and create_time like ?",
      [uuid, moment().format("YYYY-MM-DD") + "%"]
    );
    // 获取当月内个人所有的保养记录，并且按照type分组，获取每个type的数量
    // const maintainceResult = await query('select * from maintaince where uuid = ? and department = ? and shift = ? and create_time like ?', [uuid, department, shift, moment().format('YYYY-MM') + '%']);
    const maintainceResult = await query(
      "select * from maintaince where uuid = ? and department = ? and create_time like ?",
      [uuid, department, moment().format("YYYY-MM") + "%"]
    );
    // 统计每一个type的数量
    let day = 0;
    let month = 0;
    let quarter = 0;
    let halfYear = 0;
    let year = 0;
    maintainceResult.forEach((item) => {
      if (item.type === "2") {
        month++;
      } else if (item.type === "3") {
        quarter++;
      } else if (item.type === "4") {
        halfYear++;
      } else if (item.type === "5") {
        year++;
      }
    });
    // 如果有加油记录，day就是1，否则就是0
    if (oilResult.length > 0) {
      day = 1;
    } else {
      day = 0;
    }
    // 获取个人总机台
    const machineResult = await query(
      "select * from dispatch_area where uuid = ? and department = ?",
      [uuid, department]
    );
    let machine = 0;
    if (machineResult.length > 0) {
      machine = machineResult[0].machine_range;
    }
    let machineArr = [];
    if (machine) {
      machineArr = machine.split(",");
    }
    if (machineArr.length === 0) {
      machineArr = [""];
    }
    // 根据uuid获取个人今日工作项从dailywork表中
    const dailyworkResult = await query(
      "select * from dailywork where uuid = ? and create_time like ?",
      [uuid, moment().format("YYYY-MM-DD") + "%"]
    );
    const maintainceData = [
      {
        type: "日保养",
        number: day,
      },
      {
        type: "月保养",
        number: (month / machineArr.length).toFixed(2),
      },
      {
        type: "季度保养",
        number: (quarter / machineArr.length).toFixed(2),
      },
      {
        type: "半年保养",
        number: (halfYear / machineArr.length).toFixed(2),
      },
      {
        type: "年保养",
        number: (year / machineArr.length).toFixed(2),
      },
    ];

    // 用type的数量除以总机台得到进度
    const data = {
      maintaince: maintainceData,
      dailywork: dailyworkResult,
    };
    // 如果data里面的maintaince和dailywork都是空数组，就返回空数组
    if (data.maintaince.length === 0 && data.dailywork.length === 0) {
      return res.Success("获取成功", { list: [] });
    } else {
      return res.Success("获取成功", { list: data });
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 获取年度个人保养进度
router.get("/v1/getYearMaintaince", verifyToken, async (req, res) => {
  try {
    const { user } = req;
    const uuid = user.userId;
    // 根据uuid查询用户信息
    const userInfo = await client.get(`userInfo:${uuid}`);
    const department = JSON.parse(userInfo).department;
    const shift = JSON.parse(userInfo).shift;
    // 根据uuid获取当前班次加油记录
    // const oilResult = await query('select * from brush_oil where idcard = ? and create_time like ?', [uuid, moment().format('YYYY-MM-DD') + '%']);
    // 获取当月内个人所有的保养记录，并且按照type分组，获取每个type的数量
    // const maintainceResult = await query('select * from maintaince where uuid = ? and department = ? and shift = ? and create_time like ?', [uuid, department, shift, moment().format('YYYY-MM') + '%']);
    // 根据uuid获取一年12个月每日的加油记录
    const oilResult = await query(
      "select * from brush_oil where idcard = ? and create_time like ?",
      [uuid, moment().format("YYYY") + "%"]
    );
    // 获取一年12个月的保养记录
    const maintainceResult = await query(
      "select * from maintaince where uuid = ? and department = ? and shift = ? and create_time like ?",
      [uuid, department, shift, moment().format("YYYY") + "%"]
    );
    // 统计每一个type的数量
    let day = 0;
    let month = 0;
    let quarter = 0;
    let halfYear = 0;
    let year = 0;
    maintainceResult.forEach((item) => {
      if (item.type === "2") {
        month++;
      } else if (item.type === "3") {
        quarter++;
      } else if (item.type === "4") {
        halfYear++;
      } else if (item.type === "5") {
        year++;
      }
    });
    // 如果一年加油记录超过300，day就是1，否则就是0.5
    if (oilResult.length > 300) {
      day = 1;
    } else {
      day = 0.75;
    }
    // 获取个人总机台
    const machineResult = await query(
      "select * from dispatch_area where uuid = ? and department = ?",
      [uuid, department]
    );
    let machine = 0;
    if (machineResult.length > 0) {
      machine = machineResult[0].machine_range;
    }
    let machineArr = [];
    if (machine) {
      machineArr = machine.split(",");
    }
    if (machineArr.length === 0) {
      machineArr = [""];
    }
    const maintainceData = [
      {
        type: "日保养",
        number: day,
      },
      {
        type: "月保养",
        number: (month / machineArr.length).toFixed(1),
      },
      {
        type: "季度保养",
        number: (quarter / machineArr.length).toFixed(1),
      },
      {
        type: "半年保养",
        number: (halfYear / machineArr.length).toFixed(1),
      },
      {
        type: "年保养",
        number: (year / machineArr.length).toFixed(1),
      },
    ];

    // 用type的数量除以总机台得到进度
    const data = {
      maintaince: maintainceData,
    };
    // 如果data里面的maintaince和dailywork都是空数组，就返回空数组
    if (data.maintaince.length === 0 && data.dailywork.length === 0) {
      return res.Success("获取成功", { list: [] });
    } else {
      return res.Success("获取成功", { list: data });
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 获取设备总览，表名devicelist
router.get("/v1/getDeviceInfo", verifyToken, async (req, res) => {
  try {
    const { user } = req;
    const uuid = user.userId;
    // 根据uuid查询用户信息
    const userInfo = await client.get(`userInfo:${uuid}`);
    const department = JSON.parse(userInfo).department;
    // 获取部门所有设备，然后按照status分组统计数量
    const machineResult = await query(
      "select * from devicelist where department = ?",
      [department]
    );
    // 如果没有设备，就返回空数组
    if (machineResult.length === 0) {
      return res.Error("部门设备数为0");
    }
    // 统计设备状态
    let normal = 0;
    let offline = 0;
    let broken = 0;
    let spare = 0;
    machineResult.forEach((item) => {
      if (item.status === 0) {
        normal++;
      } else if (item.status === 1) {
        offline++;
      } else if (item.status === 2) {
        broken++;
      } else if (item.status === 3) {
        spare++;
      }
    });
    // 获取设备总数 = normal + offline + broken + spare
    const total = normal + offline + broken + spare;
    const data = {
      normal,
      offline,
      broken,
      spare,
      total,
    };
    return res.Success("获取成功", { list: data });
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 获取团队个人功效占比
router.get("/v1/getTeamEfficiency", verifyToken, async (req, res) => {
  try {
    const { user } = req;
    const uuid = user.userId;
    // 定义一个数组，用来存放每个员工的效率得分
    let efficiencyArr = [];
    // 根据uuid查询redis中的用户信息
    const userInfo = await client.get(`userInfo:${uuid}`);
    const department = JSON.parse(userInfo).department;
    const shift = JSON.parse(userInfo).shift;
    // 获取部门所有员工.users表，然后根据每个员工uuid去获取当前班次的日工作记录，然后统计每个员工的日工作记录
    const userResultList = await query(
      "select * from users where department = ? and level = ? and shift = ?",
      [department, 0, shift]
    );

    // 使用map方法遍历userResultList，并返回一个包含所有操作的Promise数组
    const promiseArr = userResultList.map(async (item) => {
      const userUuid = item.uuid;
      const dailyworkResult = await query(
        "select * from efficiency where uuid = ? and work_day like ?",
        [userUuid, moment().format("YYYY-MM-DD") + "%"]
      );
      // 根据uuid展示每个员工的效率得分
      let efficiency = 0;
      dailyworkResult.forEach((item) => {
        efficiency += item.score;
      });
      item.efficiency = efficiency;
      // 返回员工的效率信息
      return {
        uuid: item.uuid,
        name: item.username,
        avatar: item.avatar,
        efficiency: item.efficiency,
      };
    });

    // 等待所有异步操作的结果
    const resultArr = await Promise.all(promiseArr);
    // 将每个员工的效率信息添加到efficiencyArr数组中
    efficiencyArr = resultArr;

    return res.Success("获取成功", { list: efficiencyArr });
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 更换头像功能
router.post("/v1/changeAvatar", verifyToken, async (req, res) => {
  try {
    const { user } = req;
    const uuid = user.userId;
    const { url } = req.body;
    // 验证参数
    if (!url) {
      return res.Error("参数错误");
    }
    // 根据uuid查询用户信息
    const userInfo = await client.get(`userInfo:${uuid}`);
    const department = JSON.parse(userInfo).department;
    // 更新头像
    const result = await query(
      "update users set avatar = ? where uuid = ? and department = ?",
      [url, uuid, department]
    );
    if (result.affectedRows === 1) {
      return res.Success("更新头像成功");
    } else {
      return res.Error("更新失败");
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 获取年度团队保养进度接口
router.get("/v1/getTeamMaintaince", verifyToken, async (req, res) => {
  try {
    const { user } = req;
    const uuid = user.userId;

    const userInfo = await client.get(`userInfo:${uuid}`);
    const department = JSON.parse(userInfo).department;

    const maintainUsers = await query(
      "select uuid, username from users where department = ? and level = ?",
      [department, 0]
    );

    await Promise.all(
      maintainUsers.map(async (item) => {
        const maintainResult = await query(
          "select * from maintaince where uuid = ? and create_time like ?",
          [item.uuid, moment().format("YYYY") + "%"]
        );
        const areaList = await query(
          "select * from dispatch_area where uuid = ?",
          [item.uuid]
        );

        item.machineNum = 0;
        maintainResult.forEach((maintain) => {
          if (maintain.type === "2") {
            item.month = (item.month || 0) + 1;
          } else if (maintain.type === "3") {
            item.season = (item.season || 0) + 1;
          } else if (maintain.type === "4") {
            item.halfYear = (item.halfYear || 0) + 1;
          } else if (maintain.type === "5") {
            item.year = (item.year || 0) + 1;
          }
        });

        areaList.forEach((area) => {
          if (area.machine_range) {
            const machine_range = area.machine_range.split(",");
            item.machineNum += machine_range.length;
          }
        });

        item.machineNum = item.machineNum > 0 ? item.machineNum : 0;

        const monthEfficiency =
          item.machineNum !== 0 ? parseFloat(item.month / item.machineNum) : 0;
        const seasonEfficiency =
          item.machineNum !== 0 ? parseFloat(item.season / item.machineNum) : 0;
        const halfYearEfficiency =
          item.machineNum !== 0
            ? parseFloat(item.halfYear / item.machineNum)
            : 0;
        const yearEfficiency =
          item.machineNum !== 0 ? parseFloat(item.year / item.machineNum) : 0;

        item.month = monthEfficiency ? monthEfficiency : 0;
        item.season = seasonEfficiency ? seasonEfficiency : 0;
        item.halfYear = halfYearEfficiency ? halfYearEfficiency : 0;
        item.year = yearEfficiency ? yearEfficiency : 0;
      })
    );

    const totalDay = 1;
    const totalMonth = maintainUsers.reduce((total, item) => {
      return total + (item.month ? item.month : 0);
    }, 0);
    const totalSeason = maintainUsers.reduce((total, item) => {
      return total + (item.season ? item.season : 0);
    }, 0);
    const totalHalfYear = maintainUsers.reduce((total, item) => {
      return total + (item.halfYear ? item.halfYear : 0);
    }, 0);
    const totalYear = maintainUsers.reduce((total, item) => {
      return total + (item.year ? item.year : 0);
    }, 0);

    const result = [
      { name: "日保养", data: totalDay.toFixed(1) / 1 },
      { name: "月保养", data: totalMonth.toFixed(1) / 10 },
      { name: "季度保养", data: totalSeason.toFixed(1) / 10 },
      { name: "半年保养", data: totalHalfYear.toFixed(1) / 10 },
      { name: "年保养", data: totalYear.toFixed(1) / 10 },
    ];

    return res.Success("获取成功", { list: result });
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// app功能配置接口
router.post("/v1/appConfig", verifyToken, async (req, res) => {
  try {
    const { title, describe, icon, backColor, url } = req.body;
    // 验证参数
    if (!title || !describe || !icon || !backColor || !url) {
      return res.Error("参数验证失败", 10001);
    }
    const escaped_color_value = backColor.replace("#", "#");
    // 将配置保存到数据库
    const result = await query(
      "insert into setting (`title`, `describe`, `icon`, `backColor`, `url`) values (?, ?, ?, ?, ?)",
      [title, describe, icon, escaped_color_value, url]
    );
    if (result.affectedRows === 1) {
      return res.Success("保存成功");
    } else {
      return res.Error("保存失败");
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 获取APP配置接口
router.get("/v1/getAppConfig", verifyToken, async (req, res) => {
  try {
    const result = await query("select * from setting");
    if (result.length > 0) {
      return res.Success("获取成功", { list: result });
    } else {
      return res.Error("获取失败");
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 意见反馈接口
router.post("/v1/feedback", verifyToken, async (req, res) => {
  try {
    const { text, content } = req.body;
    const { user } = req;
    const uuid = user.userId;
    const userInfo = await client.get(`userInfo:${uuid}`);
    const department = JSON.parse(userInfo).department;
    const time = moment().format("YYYY-MM-DD HH:mm:ss");
    // 验证参数
    if (!content) {
      return res.Error("参数验证失败", 10001);
    }
    // 将意见保存到数据库
    const result = await query(
      "insert into feedback (uuid, title, content, department, create_time) values (?, ?, ?, ?, ?)",
      [uuid, text, content, department, time]
    );
    if (result.affectedRows === 1) {
      return res.Success("保存成功");
    } else {
      return res.Error("保存失败");
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 获取意见反馈列表接口
router.get("/v1/getFeedbackList", verifyToken, async (req, res) => {
  try {
    const { type } = req.query;
    const { user } = req;
    const uuid = user.userId;
    const userInfo = await client.get(`userInfo:${uuid}`);
    const department = JSON.parse(userInfo).department;
    let result = [];
    // 如果有type参数，就查询对应的意见反馈列表
    if (type !== "全部") {
      result = await query(
        "select * from feedback where department = ? and title = ? and uuid = ?",
        [department, type, uuid]
      );
    }
    if (type == "全部") {
      result = await query(
        "select * from feedback where department = ? and uuid = ?",
        [department, uuid]
      );
    }
    // 将result中的create_time字段转换为标准时间格式
    result.forEach((item) => {
      item.create_time = moment(item.create_time).format("YYYY-MM-DD HH:mm:ss");
    });
    if (result.length > 0) {
      return res.Success("获取成功", { list: result });
    } else {
      return res.Error("获取失败");
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 获取个人区域信息接口
router.get("/v1/getAreaInfo", verifyToken, async (req, res) => {
  try {
    const { user } = req;
    const uuid = user.userId;
    const userInfo = await client.get(`userInfo:${uuid}`);
    const department = JSON.parse(userInfo).department;
    const result = await query(
      "select * from dispatch_area where uuid =? and department = ?",
      [uuid, department]
    );
    // 将区域里的machine_range字段转换为数组
    result.forEach((item) => {
      item.machine_range = item.machine_range.split(",");
    });
    if (result.length > 0) {
      return res.Success("获取成功", { list: result });
    } else {
      return res.Error("获取失败");
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 获取本月内的个人工作日志接口
router.get("/v1/getWorkLog", verifyToken, async (req, res) => {
  try {
    const { user } = req;
    const uuid = user.userId;
    const userInfo = await client.get(`userInfo:${uuid}`);
    const department = JSON.parse(userInfo).department;
    // 根据uuid和department查询本月内的工作记录
    const results = await query(
      "select * from dailywork where uuid =? and depart = ? and create_time >= ? and create_time <= ?",
      [
        uuid,
        department,
        moment().startOf("month").format("YYYY-MM-DD HH:mm:ss"),
        moment().endOf("month").format("YYYY-MM-DD HH:mm:ss"),
      ]
    );
    // 将result中的create_time字段转换为标准时间格式
    results.forEach((item) => {
      item.create_time = moment(item.create_time).format("YYYY-MM-DD HH:mm:ss");
    });
    const groupedData = Object.values(
      results.reduce(function (result, obj) {
        const date = new Date(obj.create_time);
        const key = date.toISOString().split("T")[0];
        if (!result[key]) {
          result[key] = {
            dates: key,
            efficiency: 0,
            detail: [],
          };
        }
        const detailObj = {
          id: obj.id,
          content: obj.content,
          work_num: obj.work_num,
          quantity: obj.quantity,
          shift_score: obj.shift_score,
        };
        result[key].efficiency += obj.shift_score;
        result[key].detail.push(detailObj);
        return result;
      }, {})
    );
    if (results.length > 0) {
      return res.Success("获取成功", { list: groupedData });
    } else {
      return res.Error("获取失败");
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 根据保养类型获取保养项目接口
router.get("/v1/getMaintainItemByType", verifyToken, async (req, res) => {
  try {
    const { type, machine } = req.query;
    // 验证参数
    if (!type || !machine) {
      return res.Error("参数验证失败", 10001);
    }
    // 获取个人信息
    const { user } = req;
    const uuid = user.userId;
    const userInfo = await client.get(`userInfo:${uuid}`);
    const department = JSON.parse(userInfo).department;
    // 根据保养类型查询保养项目
    switch (type) {
      case "0":
        // 获取当月加油记录
        const oilResult = await query(
          "select * from brush_oil where idcard =? and create_time >= ? and create_time <= ?",
          [
            uuid,
            moment().startOf("month").format("YYYY-MM-DD HH:mm:ss"),
            moment().endOf("month").format("YYYY-MM-DD HH:mm:ss"),
          ]
        );

        if (oilResult.length > 0) {
          // 将result中的create_time字段转换为年月日格式，不要时分秒
          oilResult.forEach((item) => {
            item.create_time = moment(item.create_time).format("YYYY-MM-DD");
          });
          // 获取日保养项目的项目
          const dayResult = await query(
            "select * from maintaince_list where classify_id = ? and department = ?",
            [1, department]
          );
          // 将result按照create_time字段分组
          const oilGroupedData = Object.values(
            oilResult.reduce(function (result, obj) {
              const date = new Date(obj.create_time);
              const key = date.toISOString().split("T")[0];
              if (!result[key]) {
                result[key] = {
                  dates: key,
                  total: 0, // 添加total字段并初始化为0
                  detail: [],
                };
              }
              result[key].detail = dayResult;
              result[key].total = 60; // 每次遍历时增加total的值
              return result;
            }, {})
          );
          return res.Success("获取成功", { list: oilGroupedData });
        } else {
          return res.Error("获取失败");
        }
        break;
      case "1":
        // 获取当前type类型的当月保养记录
        const result = await query(
          "select * from maintaince where uuid =? and department = ? and type = ? and device_number = ? and create_time >= ? and create_time <= ?",
          [
            uuid,
            department,
            2,
            machine,
            moment().startOf("month").format("YYYY-MM-DD HH:mm:ss"),
            moment().endOf("month").format("YYYY-MM-DD HH:mm:ss"),
          ]
        );
        if (result.length > 0) {
          // 将result中的create_time字段转换为年月日格式，不要时分秒
          result.forEach((item) => {
            item.create_time = moment(item.create_time).format("YYYY-MM-DD");
          });
          // 获取月保养项目的项目
          const monthResult = await query(
            "select * from maintaince_list where classify_id = ? and department = ?",
            [2, department]
          );
          // 根据时间分组
          const groupedData = Object.values(
            result.reduce(function (result, obj) {
              const date = new Date(obj.create_time);
              const key = date.toISOString().split("T")[0];
              if (!result[key]) {
                result[key] = {
                  dates: key,
                  total: 0, // 添加total字段并初始化为0
                  detail: [],
                };
              }
              result[key].detail = monthResult;
              result[key].total += 1; // 每次遍历时增加total的值
              return result;
            }, {})
          );

          return res.Success("获取成功", { list: groupedData });
        } else {
          return res.Error("获取失败");
        }
        break;
      case "2":
        const quarterResult = await query(
          "select * from maintaince where uuid = ? and department = ? and type = ? and device_number = ? and create_time >= ? and create_time <= ?",
          [
            uuid,
            department,
            3,
            machine,
            moment().startOf("month").format("YYYY-MM-DD HH:mm:ss"),
            moment().endOf("month").format("YYYY-MM-DD HH:mm:ss"),
          ]
        );

        if (quarterResult.length > 0) {
          // 将result中的create_time字段转换为年月日格式，不要时分秒
          quarterResult.forEach((item) => {
            item.create_time = moment(item.create_time).format("YYYY-MM-DD");
          });

          // 获取季度保养项目的项目
          const quarterList = await query(
            "select * from maintaince_list where classify_id = ? and department = ?",
            [3, department]
          );

          // 根据时间分组
          const groupByDate = quarterResult.reduce(function (result, obj) {
            const date = obj.create_time;
            if (!result[date]) {
              result[date] = {
                dates: date,
                total: 0,
                detail: quarterList,
              };
            }
            result[date].total += 1;
            return result;
          }, {});

          // 将分组结果转为数组
          const list = Object.values(groupByDate);

          return res.Success("获取成功", { list });
        } else {
          return res.Error("获取失败");
        }

      case "3":
        // 获取当前type类型的半年保养记录
        const halfYearResult = await query(
          "select * from maintaince where uuid = ? and department = ? and type = ? and device_number = ? and create_time >= ? and create_time <= ?",
          [
            uuid,
            department,
            4,
            machine,
            moment().startOf("month").format("YYYY-MM-DD HH:mm:ss"),
            moment().endOf("month").format("YYYY-MM-DD HH:mm:ss"),
          ]
        );
        if (halfYearResult.length > 0) {
          // 将result中的create_time字段转换为年月日格式，不要时分秒
          halfYearResult.forEach((item) => {
            item.create_time = moment(item.create_time).format("YYYY-MM-DD");
          });
          // 获取半年保养项目的项目
          const halfYearList = await query(
            "select * from maintaince_list where classify_id = ? and department = ?",
            [4, department]
          );
          // 根据时间分组
          const halfYearGroupedData = halfYearResult.reduce(function (
            result,
            obj
          ) {
            const date = obj.create_time;
            if (!result[date]) {
              result[date] = {
                dates: date,
                total: 0,
                detail: halfYearList,
              };
            }
            result[date].total += 1;
            return result;
          },
          {});
          // 将分组结果转为数组
          const list = Object.values(halfYearGroupedData);
          return res.Success("获取成功", { list });
        } else {
          return res.Error("获取失败");
        }
        break;
      case "4":
        // 获取当前type类型的年度保养记录
        const yearResult = await query(
          "select * from maintaince where uuid =? and department = ? and type = ? and device_number = ? and create_time >= ? and create_time <= ?",
          [
            uuid,
            department,
            5,
            machine,
            moment().startOf("month").format("YYYY-MM-DD HH:mm:ss"),
            moment().endOf("month").format("YYYY-MM-DD HH:mm:ss"),
          ]
        );
        if (yearResult.length > 0) {
          // 将result中的create_time字段转换为年月日格式，不要时分秒
          yearResult.forEach((item) => {
            item.create_time = moment(item.create_time).format("YYYY-MM-DD");
          });
          // 获取年度保养项目的项目
          const yearList = await query(
            "select * from maintaince_list where classify_id = ? and department = ?",
            [5, department]
          );
          // 根据时间分组
          const yearGroupedData = yearResult.reduce(function (result, obj) {
            const date = obj.create_time;
            if (!result[date]) {
              result[date] = {
                dates: date,
                total: 0,
                detail: yearList,
              };
            }
            result[date].total += 1;
            return result;
          }, {});
          // 将分组结果转为数组
          const list = Object.values(yearGroupedData);
          return res.Success("获取成功", { list });
        } else {
          return res.Error("获取失败");
        }
        break;
      default:
        return res.Error("获取失败");
        break;
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 获取设备保养总览接口,获取该用户一年内的所有保养记录，根据type类型分组，计算出每一个type类型的保养次数
router.get("/v1/getMaintainceTotal", verifyToken, async (req, res) => {
  try {
    const { user } = req;
    const uuid = user.userId;
    const userInfo = await client.get(`userInfo:${uuid}`);
    const department = JSON.parse(userInfo).department;

    // 获取该用户的区域以及获取区域对应的机台
    const areaList = await query(
      "select * from dispatch_area where uuid = ? and department = ?",
      [uuid, department]
    );
    if (areaList.length === 0) {
      return res.Error("用户区域信息不存在");
    }

    const area = areaList[0].area;
    const machineRange = areaList[0].machine_range;

    // 根据区域获取一年内的所有加油记录
    const brushOilList = await query(
      "select * from brush_oil where idcard = ? and location = ? and create_time >= ? and create_time <= ?",
      [
        uuid,
        area,
        moment().startOf("year").format("YYYY-MM-DD HH:mm:ss"),
        moment().endOf("year").format("YYYY-MM-DD HH:mm:ss"),
      ]
    );
    // 根据brushOilList中的create_time来判断是哪一个月的记录，按照月份分组
    const groupByDate = brushOilList.reduce(function (result, obj) {
      const date = moment(obj.create_time).format("MM");
      if (!result[date]) {
        result[date] = {
          dates: date,
          total: 0,
        };
      }
      result[date].total += 1;
      return result;
    }, {});

    // 将分组结果转为数组
    const list = Object.values(groupByDate);
    // 根据区域获取一年内的所有保养记录
    const maintainceList = await query(
      "select * from maintaince where uuid = ? and department = ? and create_time >= ? and create_time <= ?",
      [
        uuid,
        department,
        moment().startOf("year").format("YYYY-MM-DD HH:mm:ss"),
        moment().endOf("year").format("YYYY-MM-DD HH:mm:ss"),
      ]
    );
    // 根据device_number分组,计算出每一个device_number,每一个type类型的保养次数
    const result = maintainceList.reduce((acc, item) => {
      const index = acc.findIndex(
        (value) => value.device_number === item.device_number
      );
      const type = item.type.toString();
      const name =
        type === "2"
          ? "月保养"
          : type === "3"
          ? "季度保养"
          : type === "4"
          ? "半年保养"
          : "年保养";

      if (index === -1) {
        acc.push({
          device_number: item.device_number,
          maintainceList: [
            {
              id: item.id,
              type,
              name,
              times: 1,
            },
          ],
        });
      } else {
        const typeIndex = acc[index].maintainceList.findIndex(
          (value) => value.type === type
        );
        if (typeIndex === -1) {
          acc[index].maintainceList.push({
            id: item.id,
            type,
            name,
            times: 1,
          });
        } else {
          acc[index].maintainceList[typeIndex].times += 1;
        }
      }
      return acc;
    }, []);
    // 将machineRange转为数组
    const machineRangeList = machineRange.split(",");
    // 如果list中的当前月份的total不为0，就往result中device_number相同的项中的maintainceList中添加一条记录，item.type = 1,表示日保养,times = list中的total
    list.forEach((item) => {
      if (item.total > 0) {
        result.forEach((value) => {
          if (machineRangeList.includes(value.device_number)) {
            value.maintainceList.push({
              type: "1",
              name: "日保养",
              times: item.total,
            });
          }
        });
      } else {
        result.forEach((value) => {
          if (machineRangeList.includes(value.device_number)) {
            value.maintainceList.push({
              type: "1",
              name: "日保养",
              times: 0,
            });
          }
        });
      }
    });

    // 如果result中的maintainceList的type不足4个，说明该设备没有进行过某个类型的保养，将该类型的保养次数设置为0
    result.forEach((item) => {
      // 按照type类型排序
      item.maintainceList.sort((a, b) => a.type - b.type);

      const typeList = ["2", "3", "4", "5"];
      typeList.forEach((type) => {
        if (
          item.maintainceList.findIndex((value) => value.type === type) === -1
        ) {
          item.maintainceList.push({
            type,
            name:
              type === "2"
                ? "月保养"
                : type === "3"
                ? "季度保养"
                : type === "4"
                ? "半年保养"
                : "年保养",
            times: 0,
          });
        }
      });
    });
    if (result.length > 0) {
      return res.Success("获取成功", { list: result });
    } else {
      return res.Error("获取失败");
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 获取版本更新
router.get("/v1/getVersion", verifyToken, async (req, res) => {
  try {
    const { ver } = req.query;
    const version = await query(
      "select * from version order by create_time desc limit 1",
      []
    );
    if (version.length > 0 && version[0].version !== ver) {
      return res.Success("获取成功", { list: version[0] });
    } else {
      return res.Error("获取失败");
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 用户刷卡登录接口
router.post("/v1/scanLogin", async (req, res) => {
  try {
    const { uuid } = req.body;
    // 验证参数
    if (!uuid) {
      return res.Error("参数验证失败", 10001);
    }
    // 查询当前用户是否存在
    const result = await query("select * from users where uuid = ?", [uuid]);
    if (result.length === 0) {
      return res.Error("账户不存在，请联系管理员", 10003);
    }
    // 验证是否是管理员
    if (result[0].level === 1) {
      return res.Error("仅限员工账号登录", 10008);
    }
    // 定义生成token的数据,简短token长度
    const obj = {
      userId: uuid,
      username: result[0].username,
    };
    // 生成token
    const token = gentoken(obj);
    // 将token存入redis
    client.set(`userToken:${uuid}`, token, "EX", 604800);
    // 更新token
    await query("update users set token = ? where uuid = ?", [token, uuid]);
    // 将用户信息存入redis
    client.set(`userInfo:${uuid}`, JSON.stringify(result[0]), "EX", 604800);
    // 将token加入到返回数据中
    result[0].token = token;
    // 删除密码
    delete result[0].password;
    if (result.length > 0) {
      return res.Success("登录成功", { list: result[0] });
    } else {
      return res.Error("登录失败");
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理登录接口
router.post("/v1/backend_login", async (req, res) => {
  try {
    const { username, password } = req.body;
    const ip = getClientIp(req);
    const browser = getAgent(req);
    // 去掉ip前面的::ffff:
    const newIp = ip.replace(/::ffff:/, "");
    // 验证参数
    if (!username || !password) {
      return res.Error("参数验证失败", 10001);
    }
    // 查询当前用户是否存在
    const result = await query(
      "select * from users where username = ? and level = ?",
      [username, 1]
    );
    if (result.length === 0) {
      return res.Error("该账户不存在或权限不足");
    }
    // 验证密码是否正确
    if (result[0].password !== encryptPassword(password)) {
      return res.Error("密码错误");
    }
    // 定义生成token的数据
    const obj = {
      userId: result[0].uuid,
      email: result[0].email ? result[0].email : "",
      username: result[0].username,
    };
    // 生成token
    const token = gentoken(obj);
    // 将token存入redis
    client.set(`userToken:${result[0].uuid}`, token, "EX", 604800);
    // 更新token
    await query("update users set token = ? where uuid = ?", [
      token,
      result[0].uuid,
    ]);
    // 将用户信息存入redis
    client.set(
      `userInfo:${result[0].uuid}`,
      JSON.stringify(result[0]),
      "EX",
      604800
    );
    // 根据uuid获取用户的角色信息
    const role = await query("select * from roles where uuid = ?", [
      result[0].uuid,
    ]);
    result[0].role = role[0];
    // 将token加入到返回数据中
    result[0].token = token;
    // 删除密码
    delete result[0].password;
    delete result[0].id;
    delete result[0].create_time;
    if (result.length > 0) {
      const time = moment().format("YYYY-MM-DD HH:mm:ss");
      // 记录登录日志
      const log = query(
        "insert into log (ip_addr, browser_info, username, login_time) values (?, ?, ?, ?)",
        [newIp, browser.source, result[0].username, time]
      );
      if (log.affectedRows === 0) {
        return res.Error("登录日志记录失败");
      } else {
        return res.Success("登录成功", { list: result[0] });
      }
    } else {
      return res.Error("登录失败");
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理禁用用户接口
router.post("/v1/disableUser", verifyToken, async (req, res) => {
  try {
    const { uuid } = req.body;
    // 验证参数
    if (!uuid) {
      return res.Error("参数验证失败", 10001);
    }
    // 查询当前用户是否存在
    const result = await query("select * from users where uuid = ?", [uuid]);
    if (result.length === 0) {
      return res.Error("该用户不存在或未初始化");
    }
    if (result[0].is_ban === 1) {
      const result = await query("update users set is_ban = ? where uuid = ?", [
        0,
        uuid,
      ]);
      if (result.affectedRows > 0) {
        return res.Success("解锁成功");
      } else {
        return res.Error("解锁失败");
      }
    }
    // 禁用用户
    if (result[0].is_ban === 0) {
      const result = await query("update users set is_ban = ? where uuid = ?", [
        1,
        uuid,
      ]);
      if (result.affectedRows > 0) {
        return res.Success("锁定用户成功");
      } else {
        return res.Error("锁定用户失败");
      }
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 删除用户接口
router.post("/v1/deleteUser", verifyToken, async (req, res) => {
  try {
    const { uuid } = req.body;
    // 验证参数
    if (!uuid) {
      return res.Error("参数验证失败", 10001);
    }
    // 查询当前用户是否存在
    const result = await query("select * from users where uuid = ?", [uuid]);
    if (result.length === 0) {
      return res.Error("该用户不存在");
    }
    // 修改is_del字段为1
    const resList = await query("update users set is_del = ? where uuid = ?", [
      1,
      uuid,
    ]);
    if (resList.affectedRows > 0) {
      return res.Success("删除成功");
    }
    return res.Error("删除失败");
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 获取用户列表接口
router.get("/v1/getUserLists", verifyToken, async (req, res) => {
  try {
    const { keyword, department, pageNum, pageSize } = req.query;
    const pages = pageNum ? pageNum : 1;
    const sizes = pageSize ? pageSize : 8;
    const start = (pages - 1) * sizes;
    const end = sizes;
    // 如果有keyword，department参数，就按照条件查询
    if (keyword && department) {
      const result = await query(
        `select * from users where is_del = 0 and username like '%${keyword}%' or uuid like '%${keyword}%' and department = '${department}' limit ${start},${end}`
      );
      // 根据uuid获取保养区域
      for (let i = 0; i < result.length; i++) {
        // 删除密码
        delete result[i].password;
        result[i].create_time = moment(result[i].create_time).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        const areaList = await query(
          `select * from dispatch_area where uuid = '${result[i].uuid}'`
        );
        if (areaList.length > 0) {
          result[i].area = areaList[0].area;
        }
      }
      const total = await query(
        `select count(*) as total from users where is_del = 0 and username like '%${keyword}%' and department = '${department}'`
      );
      if (result.length > 0) {
        return res.Success("获取成功", { list: result, total: total[0].total });
      }
      return res.Error("暂无相关数据");
    }
    if (keyword && !department) {
      const result = await query(
        `select * from users where is_del = 0  and username like '%${keyword}%' or uuid like '%${keyword}%' limit ${start},${end}`
      );
      // 根据uuid获取保养区域
      for (let i = 0; i < result.length; i++) {
        // 删除密码
        delete result[i].password;
        result[i].create_time = moment(result[i].create_time).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        const areaList = await query(
          `select * from dispatch_area where uuid = '${result[i].uuid}'`
        );
        if (areaList.length > 0) {
          result[i].area = areaList[0].area;
        }
      }
      const total = await query(
        `select count(*) as total from users where is_del = 0 and username like '%${keyword}%'`
      );
      if (result.length > 0) {
        return res.Success("获取成功", { list: result, total: total[0].total });
      }
      return res.Error("暂无相关数据");
    }
    if (department && !keyword) {
      const result = await query(
        `select * from users where is_del = 0  and department like '%${department}%' limit ${start},${end}`
      );
      // 根据uuid获取保养区域
      for (let i = 0; i < result.length; i++) {
        // 删除密码
        delete result[i].password;
        result[i].create_time = moment(result[i].create_time).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        const areaList = await query(
          `select * from dispatch_area where uuid = '${result[i].uuid}'`
        );
        if (areaList.length > 0) {
          result[i].area = areaList[0].area;
        }
      }
      const total = await query(
        `select count(*) as total from users where is_del = 0 and department = '${department}'`
      );
      if (result.length > 0) {
        return res.Success("获取成功", { list: result, total: total[0].total });
      }
      return res.Error("暂无相关数据");
    }
    // 查询所有用户
    const result = await query(
      `select * from users where is_del = 0  order by id desc limit ${start},${end}`
    );
    // 根据uuid获取保养区域
    for (let i = 0; i < result.length; i++) {
      // 删除密码
      delete result[i].password;
      result[i].create_time = moment(result[i].create_time).format(
        "YYYY-MM-DD HH:mm:ss"
      );
      const areaList = await query(
        `select * from dispatch_area where uuid = '${result[i].uuid}'`
      );
      if (areaList.length > 0) {
        result[i].area = areaList[0].area;
      }
    }
    const total = await query(
      `select count(*) as total from users where is_del = 0`
    );
    if (result.length > 0) {
      return res.Success("获取成功", { list: result, total: total[0].total });
    }
    return res.Error("暂无相关数据");
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台获取区域列表接口
router.get("/v1/getAreaLists", verifyToken, async (req, res) => {
  try {
    const { keyword, pageNum, pageSize } = req.query;
    const pages = pageNum ? pageNum : 1;
    const sizes = pageSize ? pageSize : 8;
    const start = (pages - 1) * sizes;
    const end = sizes;
    // 定义一个where条件
    let where = "";
    if (keyword) {
      where = `where is_del = 0 and area like '%${keyword}%'`;
    }
    // 查询所有区域
    const result = await query(
      `select * from dispatch_area ${where} order by id desc limit ${start},${end}`
    );
    const total = await query(
      `select count(*) as total from dispatch_area ${where}`
    );
    if (result.length > 0) {
      // 如果有keyword参数，就按照条件查询
      // if (keyword) {
      //     // 获取is_del > 0的区域列表
      //     const result = await query(`select * from dispatch_area where is_del < 1 and department like '%${keyword}%' limit ${start},${end}`)
      //     const total = await query(`select count(*) as total from dispatch_area where is_del < 1 and department like '%${keyword}%'`)
      //     if (result.length > 0) {
      //         // 将result中的machine_range字段转换成数组
      //         for (let i = 0; i < result.length; i++) {
      //             // 根据uuid获取姓名
      //             const userList = await query(`select * from users where uuid = '${result[i].uuid}'`)
      //             if (userList.length > 0) {
      //                 result[i].username = userList[0].username
      //                 // 头像
      //                 result[i].avatar = userList[0].avatar
      //             }
      //             result[i].total = total[0].total
      //             result[i].machine_range = result[i].machine_range.split(',')
      //         }
      //         return res.Success('获取成功', { list: result });
      //     }
      //     return res.Error('暂无相关数据');
      // }
      // 查询所有区域
      // const result = await query(`select * from dispatch_area where is_del < 1 limit ${start},${end}`)
      // const total = await query(`select count(*) as total from dispatch_area where is_del < 1`)
      // if (result.length > 0) {
      //     // 将result中的machine_range字段转换成数组
      //     for (let i = 0; i < result.length; i++) {
      //         // 根据uuid获取姓名
      //         const userList = await query(`select * from users where uuid = '${result[i].uuid}'`)
      //         if (userList.length > 0) {
      //             result[i].username = userList[0].username
      //             // 头像
      //             result[i].avatar = userList[0].avatar
      //         }
      //         result[i].total = total[0].total
      //         result[i].machine_range = result[i].machine_range.split(',')
      //     }
      //     return res.Success('获取成功', { list: result, total });
      // }
      // 将result中的machine_range字段转换成数组
      for (let i = 0; i < result.length; i++) {
        // 根据uuid获取姓名
        const userList = await query(
          `select * from users where uuid = '${result[i].uuid}'`
        );
        if (userList.length > 0) {
          result[i].username = userList[0].username;
          // 头像
          result[i].avatar = userList[0].avatar;
        }
        result[i].total = total[0].total;
        result[i].machine_range = result[i].machine_range.split(",");
      }
      return res.Success("获取成功", { list: result, total });
    }
    return res.Success("获取成功", { list: [], total: 0 });
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台删除单个区域接口
router.post("/v1/delOneAreas", verifyToken, async (req, res) => {
  try {
    const { aid } = req.body;
    const result = await query(
      `update dispatch_area set is_del = 1 where id = ${aid}`
    );
    if (result.affectedRows > 0) {
      return res.Success("删除成功");
    }
    return res.Error("删除失败");
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});
// 后台删除多个区域接口
router.post("/v1/delMoreAreas", verifyToken, async (req, res) => {
  try {
    const { areaList } = req.body;
    const promises = [];

    for (const item of areaList) {
      promises.push(
        query(`update dispatch_area set is_del = 1 where id = ${item}`).catch(
          (error) => {
            throw new Error(`更新失败: ${error.message}`);
          }
        )
      );
    }

    await Promise.all(promises);

    return res.Success("删除成功");
  } catch (error) {
    return res.Error("删除失败: " + error.message, 500);
  }
});

// 用户权限分配接口
router.post("/v1/modifyRole", verifyToken, async (req, res) => {
  try {
    const { uuid, roles } = req.body;
    let level = 0;
    let role = roles === "超级管理员" ? "admin" : "user";
    level = roles === "超级管理员" ? 1 : 0;

    const result = await query(
      `update users set level = '${level}' where uuid = '${uuid}'`
    );

    if (result.affectedRows > 0) {
      const result2 = await query(
        "insert into roles (uuid, role) values (?, ?)",
        [uuid, role]
      );

      if (result2.affectedRows > 0) {
        return res.Success("修改成功");
      }
    }

    return res.Error("修改失败");
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台获取保养项目列表接口
router.get("/v1/getMaintainceLists", verifyToken, async (req, res) => {
  try {
    const { keyword, page, pageSize } = req.query;
    const pages = page ? page : 1;
    const sizes = pageSize ? pageSize : 10;
    const start = (pages - 1) * sizes;
    const end = sizes;
    // 如果有keyword参数，就按照条件查询
    if (keyword) {
      // 根据keyword查询保养项目列表
      const result = await query(
        `select * from maintaince_list where department like '%${keyword}%' limit ${start},${end}`
      );
      const total = await query(
        `select count(*) as total from maintaince_list where department like '%${keyword}%'`
      );
      if (result.length > 0) {
        // 返回数据
        return res.Success("获取成功", { list: result, total: total[0].total });
      }
      return res.Success("获取成功", { list: [], total: 0 });
    }
    // 查询所有保养项目
    const result = await query(
      `select * from maintaince_list limit ${start},${end}`
    );
    const total = await query(`select count(*) as total from maintaince_list`);
    if (result.length > 0) {
      // 返回数据
      return res.Success("获取成功", { list: result, total: total[0].total });
    }
    return res.Success("获取成功", { list: [], total: 0 });
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台删除单个保养项目接口
router.post("/v1/delMaintainceItem", verifyToken, async (req, res) => {
  try {
    const { mid } = req.body;
    // 查询保养项目是否存在
    const result = await query(
      `select * from maintaince_list where id = ${mid}`
    );
    if (result.length > 0) {
      // 删除保养项目
      const result2 = await query(
        `delete from maintaince_list where id = ${mid}`
      );
      if (result2.affectedRows > 0) {
        return res.Success("删除成功");
      }
      return res.Error("删除失败");
    }
    return res.Error("要删除的保养项目不存在");
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台获取工作日志列表接口
router.get("/v1/getWorkLists", verifyToken, async (req, res) => {
  try {
    const { uuid, shift, dates, department, page, pageSize } = req.query;
    let pages = page ? page : 1;
    let sizes = pageSize ? pageSize : 12;
    let start = (pages - 1) * sizes;
    let end = sizes;
    // 定义一个where条件，用于查询
    let where = "";
    // 如果有uid参数，就按照条件查询
    if (uuid) {
      where += ` and uuid = '${uuid}'`;
    }
    // 如果有shift参数，就按照条件查询
    if (shift) {
      where += ` and shift = '${shift}'`;
    }
    // 如果有dates参数，create_time > dates and create_time < dates + 1
    if (dates) {
      where += ` and create_time between '${dates}' and '${moment(dates)
        .add(1, "days")
        .format("YYYY-MM-DD")}'`;
    }
    // 如果有department参数，就按照条件查询
    if (department) {
      where += ` and depart = '${department}'`;
    }
    // 如果有where条件，将pageSize改为12
    if (where) {
      sizes = 12;
      start = (pages - 1) * sizes;
      end = sizes;
    }
    // 根据uuid和department查询本月内的工作记录
    const results = await query(
      `select * from dailywork where create_time between '${moment()
        .startOf("month")
        .format("YYYY-MM-DD")}' and '${moment()
        .endOf("month")
        .format(
          "YYYY-MM-DD"
        )}' ${where} order by create_time desc limit ${start},${end}`
    );
    // 获取本月内的工作记录总数
    const total = await query(
      `select count(*) as total from dailywork where create_time between '${moment()
        .startOf("month")
        .format("YYYY-MM-DD")}' and '${moment()
        .endOf("month")
        .format("YYYY-MM-DD")}' ${where}`
    );
    // 将result中的create_time字段转换为标准时间格式
    results.forEach((item) => {
      item.create_time = moment(item.create_time).format("YYYY-MM-DD");
    });

    const groupedData = results.reduce((result, obj) => {
      const key = obj.uuid;
      const key1 = obj.create_time;

      if (!result[key]) {
        result[key] = [];
      }

      // 检查该uuid在该日期下是否已记录过工作项
      const existingDate = result[key].find((d) => d.date === key1);

      if (existingDate) {
        // 如果已记录过工作项，则将当前工作项添加到已记录的工作项中
        existingDate.details.push({
          content: obj.content + "/" + obj.work_num,
          efficiency: obj.shift_score,
          machineList: obj.deviceList.split(",").map((item) => {
            return item.split("-")[0];
          }),
        });

        // 更新efficiency的值
        existingDate.efficiency += obj.shift_score;
      } else {
        // 创建新的日期记录，并将当前工作项添加到新日期的工作项中
        result[key].push({
          id: obj.id,
          date: key1,
          username: obj.uuid,
          shift: obj.shift,
          department: obj.depart,
          efficiency: obj.shift_score,
          details: [
            {
              content: obj.content + "/" + obj.work_num,
              efficiency: obj.shift_score,
              machineList: obj.deviceList.split(",").map((item) => {
                return item.split("-")[0];
              }),
            },
          ],
        });
      }

      return result;
    }, {});

    // 计算每个日期下的efficiency值
    Object.values(groupedData).forEach((arr) => {
      arr.forEach((obj) => {
        obj.efficiency = obj.details.reduce((sum, item) => {
          return sum + item.efficiency;
        }, 0);
      });
    });
    const finalData = Object.values(groupedData).flat();
    if (results.length > 0) {
      return res.Success("获取成功", {
        list: finalData,
        total: total[0].total,
      });
    } else {
      return res.Success("获取成功", { list: [], total: 0 });
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 获取工作项目列表接口
router.get("/v1/getWorkItems", verifyToken, async (req, res) => {
  try {
    const { depart, page, pageSize } = req.query;
    let pages = page ? page : 1;
    let sizes = pageSize ? pageSize : 12;
    let start = (pages - 1) * sizes;
    let end = sizes;
    // 定义一个where条件，用于查询
    let where = "";
    // 如果有depart参数，就按照条件查询
    if (depart) {
      where += ` and department = '${depart}'`;
    }
    // 查询工作项目列表
    const results = await query(
      `select * from worklist where 1=1 ${where} order by id desc limit ${start},${end}`
    );
    // 查询工作项目总数
    const total = await query(
      `select count(*) as total from worklist where 1=1 ${where}`
    );
    for (let i = 0; i < results.length; i++) {
      // 根据uuid获取姓名
      const userList = await query(
        `select * from users where uuid = '${results[i].user_id}'`
      );
      if (userList.length > 0) {
        results[i].username = userList[0].username;
      }
      // 将create_time时间戳转换为标准时间格式
      results[i].create_time = moment(results[i].create_time).format(
        "YYYY-MM-DD HH:mm:ss"
      );
    }
    if (results.length > 0) {
      return res.Success("获取成功", { list: results, total: total[0].total });
    } else {
      return res.Success("获取成功", { list: [], total: 0 });
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 删除工作项目接口
router.post("/v1/deleteWorkItem", verifyToken, async (req, res) => {
  try {
    const { id } = req.body;
    // 查询该工作项目是否存在
    const results = await query(`select * from worklist where id = '${id}'`);
    if (results.length > 0) {
      // 删除该工作项目
      await query(`delete from worklist where id = '${id}'`);
      return res.Success("删除成功");
    } else {
      return res.Error("该工作项目不存在", 404);
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台获取保全工效分析接口
router.get("/v1/getSecurityEfficiency", verifyToken, async (req, res) => {
  try {
    const { department, page, pageSize } = req.query;
    let pages = page ? page : 1;
    let sizes = pageSize ? pageSize : 8;
    let start = (pages - 1) * sizes;
    let end = sizes;
    // 定义一个where条件，用于查询
    let where = "";
    // 如果有depart参数，就按照条件查询
    if (department) {
      where += ` and department = '${department}'`;
    }
    // 根据条件查询users表，获取部门全部人员,level = 0表示保全,分页查询
    const userList = await query(
      `select id, username, uuid, department, avatar from users where level = 0 ${where} order by id desc limit ${start},${end}`
    );
    // 根据where获取users表中level = 0的总数
    const total = await query(
      `select count(*) as total from users where level = 0 ${where}`
    );
    // 循环用户去查询每个用户今日的工作量、昨日的工作量，以及整个月的工作量
    for (let i = 0; i < userList.length; i++) {
      // 查询今日工作量
      const todayWork = await query(
        `SELECT * FROM dailywork WHERE uuid = '${
          userList[i].uuid
        }' AND create_time between '${moment()
          .subtract("days")
          .startOf("day")
          .format("YYYY-MM-DD 00:00:00")}' and '${moment()
          .subtract("days")
          .endOf("day")
          .format("YYYY-MM-DD 23:59:59")}'`
      );
      // 获取昨天的日期
      const yesterday = moment().subtract(1, "days").format("YYYY-MM-DD");
      // 查询昨日工作量
      const yesterdayWork = await query(
        `select * from dailywork where uuid = '${userList[i].uuid}' and DATE_FORMAT(create_time,'%Y-%m-%d') = '${yesterday}'`
      );
      // 根据uuid查询该用户本月的所有工作量
      const monthWork = await query(
        `select * from dailywork where uuid = '${
          userList[i].uuid
        }' and DATE_FORMAT(create_time,'%Y-%m') = '${moment().format(
          "YYYY-MM"
        )}'`
      );
      // 查询昨日的功效，将昨日的工作量的shift_score相加
      let yesterdayEfficiency = 0;
      for (let j = 0; j < yesterdayWork.length; j++) {
        yesterdayEfficiency += yesterdayWork[j].shift_score;
      }
      // 查询今日的功效，将今日的工作量的shift_score相加
      let todayEfficiency = 0;
      for (let j = 0; j < todayWork.length; j++) {
        todayEfficiency += todayWork[j].shift_score;
      }
      // 获取本月的所有工作项的shift_score相加
      let monthEfficiency = 0;
      for (let j = 0; j < monthWork.length; j++) {
        monthEfficiency += monthWork[j].shift_score;
      }
      // 将create_time时间戳转换为标准时间格式
      userList[i].create_time = moment(userList[i].create_time).format(
        "YYYY-MM-DD HH:mm:ss"
      );
      // 将今日工作量、昨日工作量、本月工作量、昨日功效、今日功效、本月功效添加到用户信息中
      userList[i].todayWork = Number(todayWork.length);
      userList[i].yesterdayWork = Number(yesterdayWork.length);
      userList[i].monthWork = Number(monthWork.length);
      userList[i].yesterdayEfficiency = Number(yesterdayEfficiency.toFixed(1));
      userList[i].todayEfficiency = Number(todayEfficiency.toFixed(1));
      userList[i].monthEfficiency = Number(monthEfficiency.toFixed(1));
    }
    if (userList.length > 0) {
      return res.Success("获取成功", { list: userList, total: total[0].total });
    } else {
      return res.Success("获取成功", { list: [], total: 0 });
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理获取加油记录接口
router.get("/v1/getOilRecords", verifyToken, async (req, res) => {
  try {
    const { uuid, department, dates, page, pageSize } = req.query;
    let pages = page ? page : 1;
    let sizes = pageSize ? pageSize : 8;
    let start = (pages - 1) * sizes;
    let end = sizes;
    // 定义一个where条件，用于查询
    let where = "";
    // 如果有uuid参数，就按照条件查询
    if (uuid) {
      where += ` and idcard = '${uuid}'`;
    }
    // 如果有depart参数，就按照条件查询
    if (department) {
      where += ` and department = '${department}'`;
    }
    // 如果有dates参数，就按照条件查询
    if (dates) {
      where += ` and create_time between '${moment(dates[0]).format(
        "YYYY-MM-DD 00:00:00"
      )}' and '${moment(dates[1]).format("YYYY-MM-DD 23:59:59")}'`;
    }
    // 根据条件查询brush_oil表，分页查询
    const oilList = await query(
      `select * from brush_oil where 1 = 1 ${where} order by id desc limit ${start},${end}`
    );
    // 根据oilList中的idcard查询users表中的name
    for (let i = 0; i < oilList.length; i++) {
      const name = await query(
        `select username from users where uuid = '${oilList[i].idcard}'`
      );
      oilList[i].name = name[0].username ? name[0].username : "未知";
    }
    // 将create_time时间戳转换为标准时间格式
    for (let i = 0; i < oilList.length; i++) {
      oilList[i].create_time = moment(oilList[i].create_time).format(
        "YYYY-MM-DD HH:mm:ss"
      );
    }
    // 根据where获取brush_oil表中的总数
    const total = await query(
      `select count(*) as total from brush_oil where 1 = 1 ${where}`
    );
    // 返回数据
    if (oilList.length > 0) {
      return res.Success("获取成功", { list: oilList, total: total[0].total });
    } else {
      return res.Success("获取成功", { list: [], total: 0 });
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理部门设备报修接口
router.post("/v1/callRepair", verifyToken, async (req, res) => {
  try {
    const { devicename, devicenumber, desc, departs, way, emergency, imglist } =
      req.body;
    const { user } = req;
    const uuid = user.userId;
    const userInfo = await client.get(`userInfo:${uuid}`);
    const department = JSON.parse(userInfo).department;
    const username = JSON.parse(userInfo).username;
    // 使用joi验证参数
    const schema = Joi.object({
      devicename: Joi.string().required().error(new Error("设备名称不能为空")),
      devicenumber: Joi.string()
        .required()
        .error(new Error("设备编号不能为空")),
      desc: Joi.string().required().error(new Error("故障描述不能为空")),
      departs: Joi.string().required().error(new Error("报修部门不能为空")),
      way: Joi.string().required().error(new Error("维修方式不能为空")),
      emergency: Joi.string().required().error(new Error("请选择紧急程度")),
      imglist: Joi.string().optional(),
    });
    const { error } = schema.validate(req.body);
    if (error) {
      return res.Error(error.message, 200);
    }
    // 根据设备编号查询设备是否存在
    const device = await query(
      `select * from device_repair where device_number = '${devicenumber}'`
    );
    // 如果设备编号存在，并且status为0或者1，表示该设备正在维修中，不能再次报修
    if (device.length > 0 && (device[0].status == 0 || device[0].status == 1)) {
      return res.Error("该设备正在维修中，请耐心等待", 10009);
    }
    // 根据设备编号生成设备唯一订单号
    const orderNumber = uuidv4();
    const time = moment().format("YYYY-MM-DD HH:mm:ss");
    // 如果不存在，就添加到device_repair表中
    const result = await query(
      "insert into device_repair (oid, device_name, device_number, content, department, repair_depart, post_user, repair_way, emergency, img_url, create_time) values(?,?,?,?,?,?,?,?,?,?,?)",
      [
        orderNumber,
        devicename,
        devicenumber,
        desc,
        department,
        departs,
        username,
        way,
        emergency,
        imglist,
        time,
      ]
    );
    // 如果添加成功，返回成功信息
    if (result.affectedRows > 0) {
      return res.Success("报修成功", { oid: orderNumber });
    }
    // 如果添加失败，返回失败信息
    return res.Error("报修失败");
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理获取设备报修记录接口
router.get("/v1/getRepairRecords", verifyToken, async (req, res) => {
  try {
    const { oid, devicenumber, depart, username, page, pageSize } = req.query;
    let pages = page ? page : 1;
    let sizes = pageSize ? pageSize : 12;
    let start = (pages - 1) * sizes;
    let end = sizes;
    // 定义一个where条件，用于查询
    let where = "";
    // 如果有oid参数，就按照条件查询
    if (oid) {
      where += ` and oid = '${oid}'`;
    }
    // 如果有devicenumber参数，就按照条件查询
    if (devicenumber) {
      where += ` and device_number = '${devicenumber}'`;
    }
    // 如果有depart参数，就按照条件查询
    if (depart) {
      where += ` and repair_depart = '${depart}'`;
    }
    // 如果有username参数，就按照条件查询
    if (username) {
      where += ` and receiver_uid = '${username}'`;
    }
    // 根据条件查询device_repair表，分页查询
    const repairList = await query(
      `select * from device_repair where 1 = 1 ${where} order by id desc limit ${start},${end}`
    );
    // 获取总数
    const total = await query(
      `select count(*) as total from device_repair where 1 = 1 ${where}`
    );
    // 返回数据
    if (repairList.length > 0) {
      // 将create_time时间戳转换为标准时间格式
      for (let i = 0; i < repairList.length; i++) {
        repairList[i].create_time = moment(repairList[i].create_time).format(
          "YYYY-MM-DD HH:mm:ss"
        );
      }
      return res.Success("获取成功", {
        list: repairList,
        total: total[0].total,
      });
    } else {
      return res.Success("获取成功", { list: [], total: 0 });
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 获取报表分析数据接口
router.get("/v1/getReportData", verifyToken, async (req, res) => {
  try {
    const { date, department, shift, page, pageSize } = req.query;
    let pages = page ? page : 1;
    let sizes = pageSize ? pageSize : 8;
    let start = (pages - 1) * sizes;
    let end = sizes;
    let startTime = date ? date[0] : "";
    let endTime = date ? date[1] : "";
    // 定义一个where条件，用于查询
    let where = "";
    // 如果有startTime参数，就按照条件查询
    if (startTime) {
      where += ` and create_time >= '${startTime}'`;
    }
    // 如果有endTime参数，就按照条件查询
    if (endTime) {
      where += ` and create_time <= '${endTime}'`;
    }
    // 如果有department参数，就按照条件查询
    if (department) {
      where += ` and department = '${department}'`;
    }
    // 如果有shift参数，就按照条件查询
    if (shift) {
      where += ` and shift = '${shift}'`;
    }
    // 增加一个where条件，level = 0的用户
    where += ` and level = 0`;
    // 根据条件查询users表，分页查询
    const userList = await query(
      `select id, uuid, username from users where 1 = 1 ${where} order by id desc limit ${start},${end}`
    );
    // 获取总数
    const total = await query(
      `select count(*) as total from users where 1 = 1 ${where}`
    );
    // 如果userList为空，直接返回空数组
    if (userList.length == 0) {
      const data = {
        uuidList: [],
        totalList: [],
      };
      const data2 = {
        uuidList1: [],
        totalList1: [],
      };
      const result = [];
      // 返回数据
      return res.Success("获取成功", {
        list: result,
        total: total[0].total,
        data,
        data2,
      });
    }
    // 根据userlist中的uuid查询dailywork表中的数据，根据uuid分组，统计每个人本月，每一项content的总数
    let sql = `SELECT uuid, content, SUM(quantity) AS total FROM dailywork WHERE uuid IN (`;
    for (let i = 0; i < userList.length; i++) {
      if (i == userList.length - 1) {
        sql += `'${userList[i].uuid}'`;
      } else {
        sql += `'${userList[i].uuid}',`;
      }
    }
    sql += `) AND MONTH(create_time) = MONTH(CURRENT_DATE()) GROUP BY uuid, content`;
    const dailyworkList = await query(sql);

    const result = [];
    for (let i = 0; i < userList.length; i++) {
      const user = userList[i];
      const userDailyworkList = dailyworkList.filter(
        (item) => item.uuid === user.uuid
      );
      const details = userDailyworkList.reduce((acc, item) => {
        const quantity = item.total;
        if (quantity > 0) {
          acc.push({ [item.content]: quantity });
        }
        return acc;
      }, []);
      result.push({ uuid: user.username, details });
    }
    // 获取月度工作量排名，根据userlist中的uuid查询dailywork表中的数据，根据uuid分组，统计每个人本月内所有content的总数
    let sql1 = `
        SELECT uuid, SUM(quantity) AS total 
        FROM dailywork 
        WHERE uuid IN (`;
    for (let i = 0; i < userList.length; i++) {
      if (i == userList.length - 1) {
        sql1 += `'${userList[i].uuid}'`;
      } else {
        sql1 += `'${userList[i].uuid}',`;
      }
    }
    sql1 += `) AND MONTH(create_time) = MONTH(CURRENT_DATE()) 
        GROUP BY uuid`;
    const dailyworkLists = await query(sql1);
    // 根据dailyworkLists中的uuid获取userList中的username
    for (let i = 0; i < dailyworkLists.length; i++) {
      const dailywork = dailyworkLists[i];
      for (let j = 0; j < userList.length; j++) {
        const user = userList[j];
        if (dailywork.uuid === user.uuid) {
          dailyworkLists[i].uuid = user.username;
        }
      }
    }
    const uuidList = dailyworkLists.map((item) => item.uuid);
    const totalList = dailyworkLists.map((item) => item.total);
    //获取月度工效排名
    let sql2 = `
        SELECT uuid, SUM(shift_score) AS total 
        FROM dailywork 
        WHERE uuid IN (`;
    for (let i = 0; i < userList.length; i++) {
      if (i == userList.length - 1) {
        sql2 += `'${userList[i].uuid}'`;
      } else {
        sql2 += `'${userList[i].uuid}',`;
      }
    }
    sql2 += `) AND MONTH(create_time) = MONTH(CURRENT_DATE())
        GROUP BY uuid`;
    const dailyworkListss = await query(sql2);
    // 根据dailyworkListss中的uuid获取userList中的username
    for (let i = 0; i < dailyworkListss.length; i++) {
      const dailywork = dailyworkListss[i];
      for (let j = 0; j < userList.length; j++) {
        const user = userList[j];
        if (dailywork.uuid === user.uuid) {
          dailyworkListss[i].uuid = user.username;
        }
      }
    }
    const uuidList1 = dailyworkListss.map((item) => item.uuid);
    const totalList1 = dailyworkListss.map((item) => item.total);
    const data = {
      uuidList,
      totalList,
    };
    const data2 = {
      uuidList1,
      totalList1,
    };

    // 根据userlist中的uuid查询brush_oil表中该uuid一年内的加油总次数
    // const userUUIDs = userList.map(user => user.uuid);

    // const totalOilQuery = `SELECT idcard, COUNT(*) AS total FROM brush_oil WHERE idcard IN (${userUUIDs.map(uuid => `'${uuid}'`).join(",")}) AND MONTH(create_time) = MONTH(CURRENT_DATE()) GROUP BY idcard`;
    // const totalOilList = await query(totalOilQuery);

    // const totalMaintainceQuery = `SELECT * FROM maintaince WHERE uuid IN (${userUUIDs.map(uuid => `'${uuid}'`).join(",")}) AND create_time >= DATE_SUB(CURRENT_DATE(), INTERVAL 1 YEAR)`;
    // const totalMaintainceList = await query(totalMaintainceQuery);

    // const totalMachineQuery = `SELECT uuid, machine_range FROM dispatch_area WHERE uuid IN (${userUUIDs.map(uuid => `'${uuid}'`).join(",")}) GROUP BY uuid`;
    // const totalMachineList = await query(totalMachineQuery);

    // const maintainceData = userList.map(user => {
    //     let day = 0;
    //     let month = 0;
    //     let quarter = 0;
    //     let halfYear = 0;
    //     let year = 0;

    //     totalMaintainceList.forEach(maintaince => {
    //         if (maintaince.uuid === user.uuid) {
    //             if (maintaince.type === '2') {
    //                 month++;
    //             } else if (maintaince.type === '3') {
    //                 quarter++;
    //             } else if (maintaince.type === '4') {
    //                 halfYear++;
    //             } else if (maintaince.type === '5') {
    //                 year++;
    //             }
    //         }
    //     });

    //     totalOilList.forEach(oil => {
    //         if (oil.idcard === user.uuid && oil.total > 0) {
    //             day = 1;
    //         }else {
    //             day = 0;
    //         }
    //     });

    //     const machine_range = totalMachineList.find(item => item.uuid === user.uuid).machine_range.split(',');

    //     return {
    //         uuid: user.uuid,
    //         data: [
    //             {
    //                 type: '日保养',
    //                 number: (day / machine_range.length * 100).toFixed(2)
    //             },
    //             {
    //                 type: '月保养',
    //                 number: (month / machine_range.length * 100).toFixed(2)
    //             },
    //             {
    //                 type: '季度保养',
    //                 number: (quarter / machine_range.length * 100).toFixed(2)
    //             },
    //             {
    //                 type: '半年保养',
    //                 number: (halfYear / machine_range.length * 100).toFixed(2)
    //             },
    //             {
    //                 type: '年保养',
    //                 number: (year / machine_range.length * 100).toFixed(2)
    //             }
    //         ]
    //     };
    // });

    // const datas = [
    //     userList.map(user => user.username),
    //     maintainceData
    // ];

    // 返回数据
    if (userList.length > 0) {
      return res.Success("获取成功", {
        list: result,
        total: total[0].total,
        data,
        data2,
      });
    } else {
      return res.Success("获取成功", { list: [], total: 0 });
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理获取意见反馈列表
router.get("/v1/getFeedbackLists", verifyToken, async (req, res) => {
  try {
    const { department, status, page, pageSize } = req.query;
    let pages = page || 1;
    let pageSizes = pageSize || 10;
    let start = (pages - 1) * pageSizes;
    let end = pageSizes;

    // 定义一个wehrer条件
    let where = "WHERE 1=1";
    if (department) {
      where += ` AND department = '${department}'`;
    }
    if (status) {
      if (status == "已处理") {
        where += ` AND status = 1`;
      }
      if (status == "未处理") {
        where += ` AND status = 0`;
      }
      where += ` AND status = '${status}'`;
    }
    // 根据条件查询feedback表中的数据
    const sql = `SELECT * FROM feedback ${where} ORDER BY create_time DESC LIMIT ${start}, ${end}`;
    const result = await query(sql);
    // 根据条件查询feedback表中的数据总条数
    const sql1 = `SELECT COUNT(*) AS total FROM feedback ${where}`;
    const total = await query(sql1);
    // 返回数据
    if (result.length > 0) {
      // 将result中的uuid替换成username
      const userList = await query(`SELECT uuid, username FROM users`);
      for (let i = 0; i < result.length; i++) {
        for (let j = 0; j < userList.length; j++) {
          if (result[i].uuid === userList[j].uuid) {
            result[i].uuid = userList[j].username;
          }
        }
        result[i].create_time = moment(result[i].create_time).format(
          "YYYY-MM-DD HH:mm:ss"
        );
      }
      return res.Success("获取成功", { list: result, total: total[0].total });
    } else {
      return res.Success("获取成功", { list: [], total: 0 });
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理修改意见反馈状态
router.post("/v1/modifyFeedbackStatus", verifyToken, async (req, res) => {
  try {
    const { uid } = req.body;
    // 根据id查询feedback表中的数据
    const sql = `SELECT * FROM feedback WHERE id = '${uid}'`;
    const result = await query(sql);
    // 判断是否有数据
    if (result.length > 0) {
      // 判断status的值
      if (result[0].status === 0) {
        const time = moment().format("YYYY-MM-DD HH:mm:ss");
        // 修改status的值和更新时间
        const sql1 = `UPDATE feedback SET status = 1, update_time = '${time}' WHERE id = '${uid}'`;
        await query(sql1);
        return res.Success("修改成功");
      } else {
        // 如果已经是已处理状态，则不做任何操作
        return false;
      }
    } else {
      return res.Error("修改失败");
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理删除意见反馈
router.post("/v1/deleteFeedback", verifyToken, async (req, res) => {
  try {
    const { uid } = req.body;
    // 查询当前id是否存在
    const sql = `SELECT * FROM feedback WHERE id = '${uid}'`;
    const result = await query(sql);
    // 判断是否有数据
    if (result.length > 0) {
      // 删除当前id的数据
      const sql1 = `DELETE FROM feedback WHERE id = '${uid}'`;
      await query(sql1);
      return res.Success("删除成功");
    } else {
      return res.Error("删除失败");
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 获取登录日志
router.get("/v1/getLoginLog", verifyToken, async (req, res) => {
  try {
    const { ip, username, date, page, pageSize } = req.query;
    let pages = page || 1;
    let pageSizes = pageSize || 10;
    let start = (pages - 1) * pageSizes;
    let end = pageSizes;

    // 定义一个wehrer条件
    let where = "WHERE 1=1";
    if (ip) {
      where += ` AND ip_addr = '${ip}'`;
    }
    if (username) {
      where += ` AND username = '${username}'`;
    }
    if (date) {
      where += ` AND login_time LIKE '%${date}%'`;
    }
    // 根据条件查询login_log表中的数据
    const sql = `SELECT * FROM log ${where} ORDER BY login_time DESC LIMIT ${start}, ${end}`;
    const result = await query(sql);
    // 根据条件查询login_log表中的数据总条数
    const sql1 = `SELECT COUNT(*) AS total FROM log ${where}`;
    const total = await query(sql1);
    // 返回数据
    if (result.length > 0) {
      // 将result中的login_time转换成年月日时分秒
      for (let i = 0; i < result.length; i++) {
        result[i].login_time = moment(result[i].login_time).format(
          "YYYY-MM-DD HH:mm:ss"
        );
      }
      return res.Success("获取成功", { list: result, total: total[0].total });
    } else {
      return res.Success("获取成功", { list: [], total: 0 });
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 提交app版本信息
router.post("/v1/uploadInfo", verifyToken, async (req, res) => {
  try {
    const { version, content, url, force_update } = req.body;
    // 获取当前时间
    const time = moment().format("YYYY-MM-DD HH:mm:ss");
    // 查询当前版本号是否存在
    const sql = `SELECT * FROM version ORDER BY id DESC LIMIT 1`;
    const result = await query(sql, []);
    // 判断是否有数据
    if (result.length > 0) {
      // 判断当前版本号是否和数据库中的版本号相同，如果一样则不做任何操作
      if (result[0].version === version) {
        return res.Error("版本号相同，请检查后重新提交");
      }
      // 如果有数据，则修改当前版本号的数据
      const sql1 = `UPDATE version SET content = ?, url = ?, force_update = ?, version = ?, update_time = ? WHERE id = 1`;
      await query(sql1, [content, url, force_update, version, time]);
      return res.Success("保存成功");
    } else {
      // 如果没有数据，则添加当前版本号的数据
      const sql2 = `INSERT INTO version (version, content, url, force_update, create_time) VALUES (?, ?, ?, ?, ?)`;
      await query(sql2, [version, content, url, force_update, time]);
      return res.Success("保存成功");
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 获取app版本信息
router.get("/v1/getAppInfos", verifyToken, async (req, res) => {
  try {
    // 查询当前版本号是否存在
    const sql = `SELECT * FROM version ORDER BY id DESC LIMIT 1`;
    const result = await query(sql, []);
    // 判断是否有数据
    if (result.length > 0) {
      return res.Success("获取成功", { list: result[0] });
    } else {
      return res.Success("获取成功", {});
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});
// 后台管理保存通知公告信息接口
router.post("/v1/saveNotice", verifyToken, async (req, res) => {
  try {
    const { notice } = req.body;
    // 根据uid查询当前用户的redis中的信息
    const { user } = req;
    const uuid = user.userId;
    const userInfo = await client.get(`userInfo:${uuid}`);
    const username = JSON.parse(userInfo).username;
    const department = JSON.parse(userInfo).department;
    // 获取当前时间
    const time = moment().format("YYYY-MM-DD HH:mm:ss");
    // 根据部门获取当前部门的通知公告信息
    const sql = `SELECT * FROM message WHERE department = '${department}' ORDER BY id DESC LIMIT 1`;
    const result = await query(sql);
    // 判断是否有数据
    if (result.length > 0) {
      // 如果有则修改当前数据
      const sql1 = `UPDATE message SET message = ?, username = ?, department = ?, update_time = ? WHERE id = 1`;
      await query(sql1, [notice, username, department, time]);
      return res.Success("修改成功");
    } else {
      // 如果没有则添加当前数据
      const sql2 = `INSERT INTO message (message, username, department, create_time) VALUES (?, ?, ?, ?)`;
      await query(sql2, [notice, username, department, time]);
      return res.Success("保存成功");
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理获取通知公告信息接口
router.get("/v1/getMessage", verifyToken, async (req, res) => {
  try {
    // 根据uid查询当前用户的redis中的信息
    const { user } = req;
    const uuid = user.userId;
    const userInfo = await client.get(`userInfo:${uuid}`);
    const department = JSON.parse(userInfo).department;
    // 获取唯一一条数据
    const sql = `SELECT * FROM message WHERE department = '${department}' ORDER BY id DESC LIMIT 1`;
    const result = await query(sql);
    // 判断是否有数据
    if (result.length > 0) {
      return res.Success("获取成功", { list: result[0] });
    } else {
      return res.Success("获取成功", {});
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 保存app首页配置信息
router.post("/v1/saveSettings", verifyToken, async (req, res) => {
  try {
    const { title, describe, icon, backColor, url } = req.body;
    // 验证参数
    const schema = Joi.object({
      title: Joi.string().required().error(new Error("标题不能为空")),
      describe: Joi.string().required().error(new Error("描述不能为空")),
      icon: Joi.string().required().error(new Error("图标不能为空")),
      backColor: Joi.string().required().error(new Error("背景颜色不能为空")),
      url: Joi.string().required().error(new Error("跳转链接不能为空")),
    });
    const { error } = schema.validate(req.body);
    if (error) {
      return res.Error(error.message);
    }
    // 获取当前时间
    const time = moment().format("YYYY-MM-DD HH:mm:ss");
    // 查询当前是否已有title相同的数据
    const sql = `SELECT * FROM setting WHERE title = ?`;
    const result = await query(sql, [title]);
    // 判断是否有数据
    if (result.length > 0) {
      // 如果有则提示已存在
      return res.Error("该标题类型已存在，请不要重复提交");
    } else {
      // 如果没有则添加当前数据
      const sql1 = `INSERT INTO setting (title, \`describe\`, icon, backColor, url, create_time) VALUES (?, ?, ?, ?, ?, ?)`;
      try {
        const result = await query(sql1, [
          title,
          describe,
          icon,
          backColor,
          url,
          time,
        ]);
        if (result.affectedRows > 0) {
          return res.Success("保存成功");
        } else {
          return res.Error("保存失败");
        }
      } catch (error) {
        // 处理错误
        return res.Error("保存失败");
      }
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});
// 获取app首页配置信息
router.get("/v1/getPageSettings", verifyToken, async (req, res) => {
  try {
    // 获取setting表中的所有数据
    const sql = `SELECT * FROM setting`;
    const result = await query(sql);
    // 判断是否有数据
    if (result.length > 0) {
      return res.Success("获取成功", { list: result });
    } else {
      return res.Success("获取成功", {});
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 清除首页配置信息
router.post("/v1/clearPageSettings", verifyToken, async (req, res) => {
  try {
    // 获取setting表中的所有数据
    const sql = `DELETE FROM setting`;
    const result = await query(sql);
    // 判断是否有数据
    if (result.affectedRows > 0) {
      return res.Success("清除成功");
    } else {
      return res.Success("清除失败");
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理保存邮箱配置
router.post("/v1/saveEmailSettings", verifyToken, async (req, res) => {
  try {
    const { host, port, ssl, email, password } = req.body;
    // 验证参数
    const schema = Joi.object({
      host: Joi.string().required().error(new Error("邮箱服务器不能为空")),
      port: Joi.number().required().error(new Error("端口不能为空")),
      ssl: Joi.boolean().optional(),
      email: Joi.string().required().error(new Error("邮箱不能为空")),
      password: Joi.string().required().error(new Error("密码不能为空")),
    });
    const { error } = schema.validate(req.body);
    if (error) {
      return res.Error(error.message);
    }
    // 查询是否已有邮箱配置，如果有则修改，没有则添加
    const sql = `SELECT * FROM email_setting WHERE id = 1`;
    const result = await query(sql);
    // 判断是否有数据
    if (result.length > 0) {
      // 如果有则修改当前数据
      const sslInt = ssl ? 1 : 0;
      const sql1 = `UPDATE email_setting SET host = ?, port = ?, is_ssl = ?, email = ?, password = ? WHERE id = 1`;
      const modifyResult = await query(sql1, [
        host,
        port,
        ssl,
        email,
        password,
      ]);
      if (modifyResult.affectedRows > 0) {
        return res.Success("修改成功");
      }
      return res.Error("修改失败");
    } else {
      // 如果没有则添加当前数据
      const sslInt = ssl ? 1 : 0;
      const sql2 = `INSERT INTO email_setting (host, port, is_ssl, email, password) VALUES (?, ?, ?, ?, ?)`;
      const resultData = await query(sql2, [
        host,
        port,
        sslInt,
        email,
        password,
      ]);
      if (resultData.affectedRows > 0) {
        return res.Success("保存成功");
      }
      return res.Error("保存失败");
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 获取邮箱配置信息
router.get("/v1/getEmailSettings", verifyToken, async (req, res) => {
  try {
    // 获取setting表中的所有数据
    const sql = `SELECT * FROM email_setting WHERE id = 1 LIMIT 1`;
    const result = await query(sql);
    // 判断是否有数据
    if (result.length > 0) {
      return res.Success("获取成功", { list: result[0] });
    } else {
      return res.Success("获取成功", {});
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理获取设备列表
router.get("/v1/getEquipmentLists", verifyToken, async (req, res) => {
  try {
    const { keyword, classify, type, department, status, page, pageSize } =
      req.query;
    const pages = parseInt(page) || 1;
    const pageSizes = parseInt(pageSize) || 10;
    const start = (pages - 1) * pageSizes;

    // 获取当前年月日，跟保修截止日期比较，如果小于当前日期则设备状态为已过保
    const nowDate = moment().format("YYYY-MM-DD");

    // 构建查询条件
    const conditions = [];
    const params = [];

    conditions.push("is_delete = 0");

    if (keyword) {
      conditions.push("(device_name LIKE ? OR device_number LIKE ?)");
      params.push(`%${keyword}%`, `%${keyword}%`);
    }

    if (classify) {
      conditions.push("classify = ?");
      params.push(classify);
    }

    if (type) {
      conditions.push("type = ?");
      params.push(type);
    }

    if (department) {
      conditions.push("department = ?");
      params.push(department);
    }

    if (status) {
      const statusMap = {
        在用: 0,
        闲置: 1,
        报废: 2,
        已调拨: 3,
        维修中: 4,
        借用: 5,
      };
      conditions.push("status = ?");
      params.push(statusMap[status]);
    }

    const whereClause = conditions.length
      ? `WHERE ${conditions.join(" AND ")}`
      : "";

    // 查询数据
    const sql = `SELECT * FROM device_info ${whereClause} ORDER BY device_number DESC LIMIT ?, ?`;
    const countSql = `SELECT COUNT(*) AS total FROM device_info ${whereClause}`;

    // 添加分页参数
    params.push(start, pageSizes);

    const [result, totalResult] = await Promise.all([
      query(sql, params),
      query(countSql, params.slice(0, -2)), // 移除分页参数
    ]);

    if (result.length > 0) {
      // 格式化日期和保修状态
      result.forEach((item) => {
        item.create_time = moment(item.create_time).format("YYYY-MM-DD");

        if (item.warranty_start && item.warranty_end) {
          item.warranty_start = moment(item.warranty_start).format(
            "YYYY-MM-DD"
          );
          item.warranty_end = moment(item.warranty_end).format("YYYY-MM-DD");
          item.warranty_status =
            item.warranty_end < nowDate ? "已过保" : "保修中";
        } else {
          item.warranty_start = "";
          item.warranty_end = "";
          item.warranty_status = "未知";
        }
      });

      return res.Success("获取成功", {
        list: result,
        total: totalResult[0].total,
      });
    } else {
      return res.Success("获取成功", { list: [], total: 0 });
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理获取设备分类
router.get("/v1/getEquipmentClassify", verifyToken, async (req, res) => {
  try {
    // 获取device_classify表中的所有数据
    const sql = `SELECT * FROM device_classify`;
    const result = await query(sql);
    // 判断是否有数据
    if (result.length > 0) {
      return res.Success("获取成功", { list: result });
    } else {
      return res.Success("获取成功", { list: [] });
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});
// 后台管理获取区域列表接口
router.get("/v1/getAllAreas", verifyToken, async (req, res) => {
  try {
    // 获取location表中的所有数据
    const sql = `SELECT * FROM location`;
    const result = await query(sql);
    // 判断是否有数据
    if (result.length > 0) {
      return res.Success("获取成功", { list: result });
    } else {
      return res.Success("获取成功", { list: [] });
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});
// 添加设备分类接口
router.post("/v1/addEquipmentClassify", verifyToken, async (req, res) => {
  try {
    const { classify } = req.body;
    const { user } = req;
    const uuid = user.userId;
    // 验证参数
    const schema = Joi.object({
      classify: Joi.string().required().error(new Error("分类名称不能为空")),
    });
    const { error } = schema.validate(req.body);
    if (error) {
      return res.Error(error.message);
    }
    // 检测该分类是否已存在
    const sql1 = `SELECT * FROM device_classify WHERE class_name = ?`;
    const result1 = await query(sql1, [classify]);
    if (result1.length > 0) {
      return res.Error("该分类已存在");
    }
    // 添加分类
    const time = moment().format("YYYY-MM-DD HH:mm:ss");
    const sql = `INSERT INTO device_classify (class_name, user_id, create_time) VALUES (?, ?, ?)`;
    const result = await query(sql, [classify, uuid, time]);
    // 判断是否添加成功
    if (result.affectedRows > 0) {
      return res.Success("添加成功");
    }
    return res.Error("添加失败");
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理添加设备接口
router.post("/v1/addEquipment", verifyToken, async (req, res) => {
  try {
    const {
      name,
      machine_number,
      classify,
      type,
      spec,
      status,
      factory,
      brand,
      money,
      moneyvn,
      dates,
      warranty,
      location,
      department,
      serial,
      contact,
      img,
      isEdit,
      eid,
    } = req.body;
    const { user } = req;
    const uuid = user.userId;
    // 根据uuid获取redis中的用户信息
    const userInfo = await client.get(`userInfo:${uuid}`);
    const username = JSON.parse(userInfo).username;
    // 验证参数
    const schema = Joi.object({
      name: Joi.string().required().error(new Error("设备名称不能为空")),
      machine_number: Joi.string()
        .required()
        .error(new Error("设备编号不能为空")),
      classify: Joi.string().required().error(new Error("设备分类不能为空")),
      type: Joi.string().required().error(new Error("设备类型不能为空")),
      spec: Joi.string().required().error(new Error("设备规格型号不能为空")),
      status: Joi.string().optional().allow(""),
      location: Joi.string()
        .required()
        .error(new Error("设备所在区域不能为空")),
      department: Joi.string()
        .required()
        .error(new Error("设备责任部门不能为空")),
      isEdit: Joi.boolean().optional().allow(""),
      eid: Joi.number().optional().allow(""),
      factory: Joi.any().optional(),
      brand: Joi.any().optional(),
      money: Joi.any().optional(),
      moneyvn: Joi.any().optional(),
      dates: Joi.any().optional(),
      warranty: Joi.any().optional(),
      serial: Joi.any().optional(),
      contact: Joi.any().optional(),
      img: Joi.any().optional(),
    });
    const { error } = schema.validate(req.body);
    if (error) {
      return res.Error(error.message);
    }
    let warranty_start = "";
    let warranty_end = "";
    if (warranty) {
      warranty_start = warranty[0];
      warranty_end = warranty[1];
    } else {
      warranty_start = "2000-01-01";
      warranty_end = "2000-01-01";
    }
    // 如果status存在，如果等于正常status=0,如果等于闲置status=1,如果等于维修中status=2,如果等于报废status=3
    let status1 = 0;
    if (status === "正常") {
      status1 = 0;
    } else if (status === "闲置") {
      status1 = 1;
    } else if (status === "维修中") {
      status1 = 2;
    } else if (status === "已报废") {
      status1 = 3;
    }
    // 定义时间
    const time = moment().format("YYYY-MM-DD HH:mm:ss");
    if (isEdit) {
      // 根据eid查询当前设备是否存在
      const sql2 = `SELECT * FROM device_info WHERE id = ?`;
      const result2 = await query(sql2, [eid]);
      if (result2.length === 0) {
        return res.Error("该设备不存在");
      }
      // 如果isEdit存在，说明是编辑
      const sql = `UPDATE device_info SET device_name = ?, device_number = ?, device_location = ?, device_model = ?, type = ?, date_manufacture = ?, warranty_start = ?, warranty_end = ?, brand = ?, device_image = ?,department = ?,
            price_viet = ?, device_price = ?, supplier = ?, contact_phone = ?, classify = ?, serial = ?, create_user = ?, create_time = ?, status = ? WHERE id = ?`,
        result = await query(sql, [
          name,
          machine_number,
          location,
          spec,
          type,
          dates,
          warranty_start,
          warranty_end,
          brand,
          img,
          department,
          moneyvn,
          money,
          factory,
          contact,
          classify,
          serial,
          username,
          time,
          status1,
          eid,
        ]);
      // 判断是否编辑成功
      if (result.affectedRows > 0) {
        return res.Success("编辑成功");
      }
      return res.Error("编辑失败");
    } else {
      // 根据设备编号查询当前设备是否存在
      const sql2 = `SELECT * FROM device_info WHERE device_number = ?`;
      const result2 = await query(sql2, [machine_number]);
      if (result2.length > 0) {
        return res.Error("该设备已存在，请勿重复添加");
      }
      // isEdit不存在则添加设备
      const sql = `INSERT INTO device_info (device_name, device_number, device_location, device_model, type, date_manufacture, warranty_start, warranty_end, brand, device_image,department,
            price_viet, device_price, supplier, contact_phone, classify, serial, create_user, create_time, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?, ?, ?, ?)`,
        result = await query(sql, [
          name,
          machine_number,
          location,
          spec,
          type,
          dates,
          warranty_start,
          warranty_end,
          brand,
          img,
          department,
          moneyvn,
          money,
          factory,
          contact,
          classify,
          serial,
          username,
          time,
          status1,
        ]);
      // 判断是否添加成功
      if (result.affectedRows > 0) {
        return res.Success("添加成功");
      }
      return res.Error("添加失败");
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});
// 后台管理删除设备接口
router.post("/v1/deleteEquipment", verifyToken, async (req, res) => {
  try {
    const { device_number } = req.body;

    // 创建一个数组来存储所有删除操作的 Promise
    const deletePromises = device_number.map(async (item) => {
      const sql = "UPDATE device_info SET is_delete = 1 WHERE id = ?";
      const result = await query(sql, [item]);

      // 判断是否删除成功
      if (result.affectedRows > 0) {
        return "删除成功";
      }
      throw new Error("删除失败");
    });

    // 等待所有删除操作完成
    const deleteResults = await Promise.all(deletePromises);

    // 检查删除结果
    for (const result of deleteResults) {
      if (result !== "删除成功") {
        throw new Error("删除失败");
      }
    }

    return res.Success("删除成功");
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 添加车间地点接口
router.post("/v1/addLocation", verifyToken, async (req, res) => {
  try {
    const { name, address } = req.body;
    const { user } = req;
    const uuid = user.userId;
    // 验证参数
    const schema = Joi.object({
      name: Joi.string().required().error(new Error("车间名称不能为空")),
      address: Joi.string().required().error(new Error("车间地址不能为空")),
    });
    const { error } = schema.validate(req.body);
    if (error) {
      return res.Error(error.message);
    }
    // 查询当前name的地址是否存在
    const sql1 = `SELECT * FROM location WHERE name = ? and sub = ?`;
    const result1 = await query(sql1, [name, address]);
    // 判断是否存在
    if (result1.length > 0) {
      return res.Error("该车间地址已存在!");
    }
    // 不存在则添加车间地址
    const sql = `INSERT INTO location (name, sub, userid) VALUES (?, ?, ?)`,
      result = await query(sql, [name, address, uuid]);
    // 判断是否添加成功
    if (result.affectedRows > 0) {
      return res.Success("添加成功");
    }
    return res.Error("添加失败");
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 全局生成二维码接口
router.get("/v1/qrcode", async (req, res) => {
  try {
    const { text } = req.query;
    // 验证参数,可以使字符串也可以是数组
    const schema = Joi.alternatives().try(
      Joi.string().required().error(new Error("二维码内容不能为空")),
      Joi.array().items(
        Joi.string().required().error(new Error("二维码内容不能为空"))
      )
    );
    const { error } = schema.validate(text);
    if (error) {
      return res.Error(error.message);
    }
    // 如果传递过来的是个数组，则循环生成二维码
    if (Array.isArray(text)) {
      const qrcodePromises = text.map(async (item) => {
        const result = await QRCode.toDataURL(item);
        return result;
      });
      const qrcodeResults = await Promise.all(qrcodePromises);
      if (qrcodeResults.length > 0) {
        return res.Success("生成二维码成功", qrcodeResults);
      }
      return res.Error("生成二维码失败");
    }
    // 如果传递过来的是个字符串，则直接生成二维码
    const result = await QRCode.toDataURL(text);
    if (result) {
      return res.Success("生成二维码成功", result);
    }
    return res.Error("生成二维码失败");
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理转移设备接口
router.post("/v1/transferEquipment", verifyToken, async (req, res) => {
  try {
    const { machineList, oldDepart, newDepart, newLocation } = req.body;
    const { user } = req;
    const uuid = user.userId;
    // 根据uuid获取redis中的username
    const userInfo = await client.get(`userInfo:${uuid}`);
    const username = JSON.parse(userInfo).username;
    // 验证参数
    const schema = Joi.object({
      machineList: Joi.array().required().error(new Error("设备编号不能为空")),
      oldDepart: Joi.string().required().error(new Error("原部门不能为空")),
      newDepart: Joi.string().required().error(new Error("新部门不能为空")),
      newLocation: Joi.string()
        .required()
        .error(new Error("新存放地点不能为空")),
    });
    const { error } = schema.validate(req.body);
    if (error) {
      return res.Error(error.message);
    }
    // 创建一个数组来存储所有转移操作的 Promise
    const transferPromises = machineList.map(async (item) => {
      const sql =
        "UPDATE device_info SET department = ?, device_location = ?, status = ?, responsibile_user = ?, belong_user = ?, use_department = ? WHERE device_number = ?";
      const result = await query(sql, [newDepart, newLocation, 3, "", "", newDepart, item.numbers]);
      // 判断是否转移成功
      if (result.affectedRows > 0) {
        return "转移成功";
      }
      throw new Error("转移失败");
    });
    // 等待所有转移操作完成
    const transferResults = await Promise.all(transferPromises);
    // 检查转移结果
    for (const result of transferResults) {
      if (result !== "转移成功") {
        throw new Error("转移失败");
      }
    }
    const time = moment().format("YYYY-MM-DD HH:mm:ss");
    // 循环machineList数组将转移记录写入数据表transfer_device_log,字段，id, device_name,device_number, old_depart, new_depart, old_location, new_location, transfer_user, create_time
    const transferLogPromises = machineList.map(async (item) => {
      const sql =
        "INSERT INTO transfer_device_log (device_name, device_number, old_depart, new_depart, old_location, new_location, transfer_user, create_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
      const result = await query(sql, [
        item.names,
        item.numbers,
        oldDepart,
        newDepart,
        item.location,
        newLocation,
        username,
        time,
      ]);
      // 判断是否添加成功
      if (result.affectedRows > 0) {
        return "添加成功";
      }
      throw new Error("添加失败");
    });
    // 等待所有添加操作完成
    const transferLogResults = await Promise.all(transferLogPromises);
    // 检查添加结果
    for (const result of transferLogResults) {
      if (result !== "添加成功") {
        throw new Error("添加失败");
      }
    }
    return res.Success("转移成功");
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});
// 获取设备调拨记录接口
router.get("/v1/getTransferRecords", verifyToken, async (req, res) => {
  try {
    const { keywords, username, page, pageSize } = req.query;
    let pages = page || 1;
    let pageSizes = pageSize || 12;
    let start = (pages - 1) * pageSizes;
    let end = pageSizes;
    // 定义一个where条件
    let where = "";
    // 判断是否传递了keywords
    if (keywords) {
      where += ` AND (device_name LIKE '%${keywords}%' OR device_number LIKE '%${keywords}%')`;
    }
    // 判断是否传递了username
    if (username) {
      where += ` AND transfer_user LIKE '%${username}%'`;
    }
    // 根据条件查询数据is_delete = 0
    const sql = `SELECT * FROM transfer_device_log WHERE is_delete = 0 ${where} ORDER BY create_time DESC LIMIT ${start}, ${end}`;
    const result = await query(sql);
    // 查询总条数
    const sql2 = `SELECT COUNT(*) AS total FROM transfer_device_log WHERE is_delete = 0 ${where}`;
    const result2 = await query(sql2);
    // 返回数据
    if (result.length > 0) {
      // 将result中的create_time字段格式化
      result.forEach((item) => {
        item.create_time = moment(item.create_time).format(
          "YYYY-MM-DD HH:mm:ss"
        );
      });
      return res.Success("获取设备调拨记录成功", {
        list: result,
        total: result2[0].total,
      });
    }
    return res.Success("暂无数据", {
      list: [],
      total: 0,
    });
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 删除调拨记录接口
router.post("/v1/deleteTransferRecords", verifyToken, async (req, res) => {
  try {
    const { id } = req.body;
    // 验证参数
    const schema = Joi.object({
      id: Joi.number().required().error(new Error("id不能为空")),
    });
    const { error } = schema.validate(req.body);
    if (error) {
      return res.Error(error.message);
    }
    // 查找id对应的调拨记录是否存在
    const sql = "SELECT * FROM transfer_device_log WHERE id = ?";
    const result = await query(sql, [id]);
    if (result.length === 0) {
      return res.Error("该调拨记录不存在");
    }
    // 删除调拨记录，将is_delete字段设置为1
    const sql2 = "UPDATE transfer_device_log SET is_delete = 1 WHERE id = ?";
    const result2 = await query(sql2, [id]);
    // 判断是否删除成功
    if (result2.affectedRows > 0) {
      return res.Success("删除成功");
    }
    return res.Error("删除失败");
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台设备管理设备列表导出到excel接口
router.post("/v1/exportExcels", verifyToken, async (req, res) => {
  try {
    const { keyword, classify, type, department, status } = req.query;
    // 定义一个where条件
    let where = "";
    // 判断是否传递了keyword
    if (keyword) {
      where += ` AND (device_name LIKE '%${keyword}%' OR device_number LIKE '%${keyword}%')`;
    }
    // 判断是否传递了classify
    if (classify) {
      where += ` AND classify = '${classify}'`;
    }
    // 判断是否传递了type
    if (type) {
      where += ` AND type = '${type}'`;
    }
    // 判断是否传递了department
    if (department) {
      where += ` AND department = '${department}'`;
    }
    // 判断是否传递了status
    let status1 = "";
    if (status) {
      status === "在用"
        ? (status1 = "0")
        : status === "闲置"
        ? (status1 = "1")
        : status === "报废"
        ? (status1 = "2")
        : status === "已调拨"
        ? (status1 = "3")
        : status === "维修中"
        ? (status1 = "4")
        : (status1 = "5");
      where += ` AND status = '${status1}'`;
    }
    // 根据条件查询数据is_delete = 0
    const sql = `SELECT * FROM device_info WHERE is_delete = 0 ${where} ORDER BY create_time DESC`;
    const result = await query(sql);
    // 返回数据
    if (result.length > 0) {
      // 将result中的create_time字段格式化
      result.forEach((item) => {
        item.create_time = moment(item.create_time).format(
          "YYYY-MM-DD HH:mm:ss"
        );
      });
      // 定义excel表格的表头
      const data = [
        [
          "序号",
          "RFID",
          "设备名称",
          "设备编号",
          "设备分类",
          "资产类型",
          "规格型号",
          "所属部门",
          "存放位置",
          "出厂日期",
          "购入日期",
          "设备品牌",
          "越盾金额",
          "供应商",
          "联系电话",
          "联系人",
          "设备单价",
          "序列号",
          "设备状态",
          "保修开始时间",
          "保修结束时间",
          "设备来源",
          "单位",
          "资产负责人",
          "使用人",
          "使用部门",
          "使用期限",
          "创建人",
          "创建时间",
        ],
      ];
      // 遍历result，将数据push到data中
      result.forEach((item) => {
        // 格式化设备状态，将item.status转换为中文
        item.status =
          item.status === 0
            ? "在用"
            : item.status === 1
            ? "闲置"
            : item.status === 2
            ? "报废"
            : item.status === 3
            ? "已调拨"
            : item.status === 4
            ? "维修中"
            : "借用";
        data.push([
          item.id,
          item.rfid,
          item.device_name,
          item.device_number,
          item.classify,
          item.type,
          item.device_model,
          item.department,
          item.device_location,
          item.date_manufacture,
          item.date_buy,
          item.brand,
          item.price_viet,
          item.supplier,
          item.contact_phone,
          item.contact_name,
          item.device_price,
          item.serial,
          item.status,
          item.warranty_start,
          item.warranty_end,
          item.source,
          item.unit,
          item.responsibile_user,
          item.belong_user,
          item.use_department,
          item.validity_period,
          item.create_user,
          item.create_time,
        ]);
      });
      // 定义excel表格的名称
      const fileName = "特种面料厂设备台账列表";
      // 创建一个工作簿
      const workbook = xlsx.utils.book_new();
      // 创建一个工作表
      const worksheet = xlsx.utils.aoa_to_sheet(data);
      // 设置列宽
      worksheet["!cols"] = [
        { width: 20 }, // 设备名称列宽为15
        { width: 15 }, // 设备编号列宽为15
        { width: 15 }, // 设备分类列宽为15
        { width: 15 }, // 设备类型列宽为15
        { width: 15 }, // 设备状态列宽为15
        { width: 15 }, // 所属部门列宽为15
        { width: 15 }, // 设备位置列宽为15
        { width: 20 }, // 创建时间列宽为20
        { width: 15 }, // 设备名称列宽为15
        { width: 15 }, // 设备编号列宽为15
        { width: 15 }, // 设备分类列宽为15
        { width: 15 }, // 设备类型列宽为15
        { width: 15 }, // 设备状态列宽为15
        { width: 15 }, // 所属部门列宽为15
        { width: 15 }, // 设备位置列宽为15
        { width: 20 }, // 创建时间列宽为20
        { width: 15 }, // 设备位置列宽为15
        { width: 20 }, // 创建时间列宽为20
        { width: 15 }, // 设备位置列宽为15
        { width: 15 }, // 设备位置列宽为15
        { width: 15 }, // 设备位置列宽为15
        { width: 15 }, // 设备位置列宽为15
        { width: 15 }, // 设备位置列宽为15
        { width: 15 }, // 设备位置列宽为15
        { width: 15 }, // 设备位置列宽为15
        { width: 15 }, // 设备位置列宽为15
        { width: 15 }, // 设备位置列宽为15
        { width: 15 }, // 设备位置列宽为15
        { width: 15 }, // 设备位置列宽为15
        
      ];
      // 将工作表添加到工作簿
      xlsx.utils.book_append_sheet(workbook, worksheet, fileName);
      // 将工作簿转换为二进制数据流
      const buffer = xlsx.write(workbook, { type: "buffer", bookType: "xlsx" });
      res.setHeader(
        "Content-Type",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      );
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="${encodeURIComponent(fileName)}.xlsx"`
      );
      res.end(buffer);
    } else {
      return res.Error("暂无数据", 404);
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台设备管理设备列表导入excel接口
router.post(
  "/v1/importExcels",
  verifyToken,
  upload.single("files"),
  async (req, res) => {
    let filePath;
    try {
      // 获取上传的excel文件
      const { file } = req;
      if (!file) {
        return res.Error("请上传文件");
      }

      // 获取个人信息
      const { user } = req;
      const uuid = user.userId;
      const userInfo = await client.get(`userInfo:${uuid}`);
      if (!userInfo) {
        return res.Error("获取用户信息失败");
      }
      const department = JSON.parse(userInfo).department;
      const username = JSON.parse(userInfo).username;
      // 获取上传的excel文件信息
      const fileName = file.originalname;
      const fileType = file.mimetype;
      
      // 判断文件类型是否为excel
      if (
        fileType !==
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      ) {
        return res.Error("请上传excel文件");
      }

      // 定义存储excel文件的路径
      filePath = path.join(__dirname, "../uploads", fileName);

      try {
        // 读取excel文件
        const workbook = xlsx.readFile(filePath);
        const worksheet = workbook.Sheets[workbook.SheetNames[0]];
        const data = xlsx.utils.sheet_to_json(worksheet);

        if (!data || data.length === 0) {
          return res.Error("Excel文件内容为空");
        }

        // 开启事务
        await query("START TRANSACTION");

        // 遍历data，将数据插入到数据库中
        for (let i = 0; i < data.length; i++) {
          const item = data[i];

          // 检查必填字段
          const requiredFields = ["设备编号", "设备名称", "所属部门", "存放位置", "资产类型"];
          for (const field of requiredFields) {
            if (!item[field]) {
              await query("ROLLBACK");
              console.log(`第${i + 1}行的${field}不能为空`);
              return res.Error(`第${i + 1}行的${field}不能为空`);
            }
          }
          // 如果username为admin，则不检查部门权限
          if (username !== "admin") {
            // 检查部门权限
            if (item["所属部门"] !== department) {
              await query("ROLLBACK");
              return res.Error("你只能导入自己部门的设备");
            }
          }

          const device_number = item["设备编号"];

          // 使用参数化查询防止SQL注入
          const checkSql = "SELECT * FROM device_info WHERE device_number = ?";
          const result = await query(checkSql, [device_number]);

          if (result.length > 0) {
            // 更新设备信息
            const updateSql = `
              UPDATE device_info SET 
                device_name = ?,
                rfid = ?,
                classify = ?,
                type = ?,
                device_model = ?,
                department = ?,
                device_location = ?,
                date_manufacture = ?,
                date_buy = ?,
                brand = ?,
                price_viet = ?,
                supplier = ?,
                contact_phone = ?,
                contact_name = ?,
                device_price = ?,
                serial = ?,
                source = ?,
                unit = ?,
                responsibile_user = ?,
                belong_user = ?,
                use_department = ?,
                validity_period = ?,
                create_user = ?
              WHERE device_number = ?
            `;

            await query(updateSql, [
              item["设备名称"] || null,
              item["RFID"] || null,
              item["设备分类"] || null,
              item["资产类型"], // 修改这里,不允许为null
              item["规格型号"] || null,
              item["所属部门"],
              item["存放位置"] || null,
              item["出厂日期"] || null,
              item["购入日期"] || null,
              item["设备品牌"] || null,
              item["越盾金额"] || null,
              item["供应商"] || null,
              item["联系电话"] || null,
              item["联系人"] || null,
              item["设备单价"] || null,
              item["序列号"] || null,
              item["设备来源"] || null,
              item["单位"] || null,
              item["资产负责人"] || null,
              item["使用人"] || null,
              item["使用部门"] || null,
              item["使用期限"] || null,
              item["创建人"] || user.username,
              device_number
            ]);

          } else {
            // 插入新设备
            const insertSql = `
              INSERT INTO device_info (
                device_name, rfid, device_number, classify, type,
                device_model, department, device_location, date_manufacture,
                date_buy, brand, price_viet, supplier, contact_phone,
                contact_name, device_price, serial, source, unit,
                responsibile_user, belong_user, use_department,
                validity_period, create_user, create_time
              ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            `;

            await query(insertSql, [
              item["设备名称"],
              item["RFID"] || null,
              device_number,
              item["设备分类"] || null,
              item["资产类型"], // 修改这里,不允许为null
              item["规格型号"] || null,
              item["所属部门"],
              item["存放位置"] || null,
              item["出厂日期"] || null,
              item["购入日期"] || null,
              item["设备品牌"] || null,
              item["越盾金额"] || null,
              item["供应商"] || null,
              item["联系电话"] || null,
              item["联系人"] || null,
              item["设备单价"] || null,
              item["序列号"] || null,
              item["设备来源"] || null,
              item["单位"] || null,
              item["资产负责人"] || null,
              item["使用人"] || null,
              item["使用部门"] || null,
              item["使用期限"] || null,
              item["创建人"] || user.username,
              moment().format("YYYY-MM-DD HH:mm:ss")
            ]);
          }
        }

        // 提交事务
        await query("COMMIT");

        // 删除excel文件
        fs.unlinkSync(filePath);

        return res.Success("导入成功");

      } catch (error) {
        console.log('导入失败', error);
        // 回滚事务
        await query("ROLLBACK");
        throw error;
      }

    } catch (error) {
      console.log('服务器错误', error);
      // 确保文件被删除
      try {
        if (filePath && fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }
      } catch (e) {
        console.error("删除文件失败:", e);
      }

      return res.Error("服务器异常: " + error.message);
    }
  }
);
// 获取设备保养记录列表接口
router.get("/v1/deviceMaintainList", verifyToken, async (req, res) => {
  try {
    // 获取对象中的参数
    let {
      keywords,
      device_number,
      area,
      depart,
      type,
      shift,
      date,
      page,
      pageSize,
    } = req.query;
    let pages = page || 1;
    let pageSizes = pageSize || 12;
    let start = (pages - 1) * pageSizes;
    let end = pageSizes;
    // joi验证参数
    const schema = Joi.object({
      keywords: Joi.string().allow(""),
      device_number: Joi.string().allow(""),
      area: Joi.string().allow(""),
      depart: Joi.string().allow(""),
      type: Joi.string().allow(""),
      date: Joi.array().allow(""),
    });
    const { error } = schema.validate({
      keywords,
      device_number,
      area,
      depart,
      type,
    });
    if (error) {
      return res.Error(error.message);
    }
    // 定义一个where条件
    let where = "";
    // 判断是否有关键字
    if (keywords) {
      where += ` AND (device_number LIKE '%${keywords}%' OR uuid LIKE '%${keywords}%')`;
    }
    // 判断是否有设备编号
    if (device_number) {
      where += ` AND device_number = '${device_number}'`;
    }
    // 判断是否有区域
    if (area) {
      where += ` AND location = '${area}'`;
    }
    // 判断是否有部门
    if (depart) {
      where += ` AND department = '${depart}'`;
    }
    // 判断是否有设备类型
    if (type) {
      type === "日保养"
        ? (type = 1)
        : type === "周保养"
        ? (type = 2)
        : type === "月保养"
        ? (type = 3)
        : type === "季度保养"
        ? (type = 4)
        : type === "半年保养"
        ? (type = 5)
        : (type = 6);
      where += ` AND type = '${type}'`;
    }
    if (shift) {
      where += ` AND shift = '${shift}'`;
    }
    // 判断是否有保养时间
    if (date) {
      where += ` AND start_time >= '${date[0]}' AND end_time <= '${date[1]}'`;
    }
    // 根据条件查询数据库中的设备保养记录列表
    const sql = `SELECT * FROM maintaince WHERE 1 = 1 ${where} ORDER BY id DESC LIMIT ${start}, ${end}`;
    const result = await query(sql);
    // 根据条件查询数据库中的设备保养记录总数
    const sql1 = `SELECT COUNT(*) AS total FROM maintaince WHERE 1 = 1 ${where}`;
    const total = await query(sql1);
    // 返回数据
    if (result.length > 0) {
      // 将设备保养记录列表中的start_time和end_time转换为年月日时分秒的格式
      for (let i = 0; i < result.length; i++) {
        result[i].start_time = moment(result[i].start_time).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        result[i].end_time = moment(result[i].end_time).format(
          "YYYY-MM-DD HH:mm:ss"
        );
      }
      return res.Success("获取成功", { list: result, total: total[0].total });
    }
    return res.Success("获取成功", { list: [], total: 0 });
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理提交设备采购申请接口
router.post("/v1/submitPurchaseInfo", verifyToken, async (req, res) => {
  try {
    // 获取对象中的参数
    const {
      device_name,
      brand,
      supplier,
      quantity,
      price,
      total_price,
      inquirer,
      operator,
      reason,
      applicant,
      contact,
      manager,
      depart_manager,
      approver,
      id,
    } = req.body;
    // joi验证参数
    const schema = Joi.object({
      device_name: Joi.string().required().error(new Error("设备名称不能为空")),
      brand: Joi.string().required().error(new Error("品牌不能为空")),
      supplier: Joi.string().optional().allow(""),
      quantity: Joi.number().required().error(new Error("采购数量不能为空")),
      price: Joi.number().optional().allow(""),
      total_price: Joi.number().optional().allow(""),
      inquirer: Joi.string().optional().allow(""),
      operator: Joi.string().optional().allow(""),
      reason: Joi.string().required().error(new Error("申请原因不能为空")),
      applicant: Joi.string().required().error(new Error("申请人不能为空")),
      contact: Joi.string().required().error(new Error("联系方式不能为空")),
      manager: Joi.string().optional().allow(""),
      depart_manager: Joi.string().optional().allow(""),
      approver: Joi.string().optional().allow(""),
      id: Joi.number().optional().allow(""),
    });
    const { error } = schema.validate(req.body);
    if (error) {
      return res.Error(error.message);
    }
    // 获取当前时间
    const date = moment().format("YYYY-MM-DD HH:mm:ss");
    // 生成采购单号
    let oid = "SBCG" + moment().format("YYYYMMDDHHmmss");
    const { user } = req;
    const uuid = user.userId;
    // 根据uuid获取redis中的用户信息
    const userInfo = await client.get(`userInfo:${uuid}`);
    // 申请部门
    const department = JSON.parse(userInfo).department;
    // 如果是编辑
    if (id) {
      // 查询数据库中是否有该采购单号
      const sql1 = `SELECT * FROM device_purchase WHERE id = '${id}'`;
      const result1 = await query(sql1);
      if (result1.length === 0) {
        return res.Error("该采购单号不存在");
      }
      purchase_number = result1[0].order_number;
      // 如果说result1[0].purchase_status 大于0,说明部门经理已经审批过了,不能修改
      if (result1[0].purchase_status === 1) {
        return res.Error("该采购单号部门负责人已经审批,不能修改");
      }
      // 如果说result1[0].purchase_status大于2.说明在采购中,不能修改
      if (result1[0].purchase_status > 2) {
        return res.Error("该采购单号已经在采购中,不能修改");
      }
      // 根据id修改数据库中的设备采购申请信息
      const sql = `UPDATE device_purchase SET device_name = '${device_name}', brand = '${brand}', supplier = '${supplier}', quantity = '${quantity}', price = '${price}', total_price = '${total_price}', inquirer = '${inquirer}', operator = '${operator}', reason = '${reason}', applicant = '${applicant}', contact = '${contact}', manager = '${manager}', depart_manager = '${depart_manager}', approver = '${approver}', department = '${department}', order_number = '${purchase_number}' WHERE id = '${id}'`;
      const result = await query(sql);
      if (result.affectedRows > 0) {
        return res.Success("修改成功");
      }
      return res.Error("修改失败");
    } else {
      // 将数据插入数据库中,表名为device_purchase,字段名为device_name, brand, supplier, quantity, operator, reason, applicant, contact, price, total_price, inquirer, manager, depart_manager, approver, order_number, create_time, department
      const sql = `INSERT INTO device_purchase (device_name, brand, supplier, quantity, operator, reason, applicant, contact, price, total_price, inquirer, manager, depart_manager, approver, order_number, create_time, department) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;
      const values = [
        device_name,
        brand,
        supplier,
        quantity,
        operator,
        reason,
        applicant,
        contact,
        price,
        total_price,
        inquirer,
        manager,
        depart_manager,
        approver,
        oid,
        date,
        department,
      ];
      const result = await query(sql, values);
      const mail = "<EMAIL>";
      const msg = department;
      const results = await getEmail(mail, msg);
      if (result.affectedRows > 0 && results.length > 0) {
        return res.Success("提交成功，请等待审核");
      }
      return res.Error("提交失败");
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理获取设备采购申请列表接口
router.get("/v1/getPurchaseInfoList", verifyToken, async (req, res) => {
  try {
    // 获取对象中的参数
    const { keywords, username, department, date, status, page, pageSize } =
      req.query;
    let pages = page || 1;
    let pageSizes = pageSize || 10;
    let start = (pages - 1) * pageSizes;
    let end = pageSizes;
    let where = "";
    // 判断是否有关键字
    if (keywords) {
      where += ` AND (device_name LIKE '%${keywords}%')`;
    }
    // 判断是否有采购经办人
    if (username) {
      where += ` AND operator = '${username}'`;
    }
    // 判断是否有申请部门
    if (department) {
      where += ` AND department = '${department}'`;
    }
    // 判断是否有采购时间,有则根据采购时间查询,时间格式是字符串 2023-08-17，获取create_time在这个时间段内的数据
    if (date) {
      where += ` AND create_time LIKE '%${date}%'`;
    }
    // 判断是否有采购状态
    let status1 = "";
    if (status) {
      if (status === "待审批") {
        status1 = "0";
      } else if (status === "询价中") {
        status1 = "1";
      } else if (status === "审批中") {
        status1 = "2";
      } else if (status === "采购中") {
        status1 = "3";
      } else {
        status1 = "4";
      }
      where += ` AND status = '${status1}'`;
    }
    // 根据条件查询数据库中的设备采购申请列表
    const sql = `SELECT * FROM device_purchase WHERE 1 = 1 ${where} ORDER BY create_time DESC LIMIT ${start}, ${end}`;
    const result = await query(sql);
    // 根据条件查询数据库中的设备采购申请列表的总条数
    const sql1 = `SELECT COUNT(*) AS total FROM device_purchase WHERE 1 = 1 ${where}`;
    const total = await query(sql1);
    if (result.length > 0) {
      for (let i = 0; i < result.length; i++) {
        result[i].create_time = moment(result[i].create_time).format(
          "YYYY-MM-DD HH:mm:ss"
        );
      }
      return res.Success("获取成功", { list: result, total: total[0].total });
    }
    return res.Success("获取成功", { list: [], total: 0 });
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理获取巡检记录列表接口
router.get("/v1/getInspectionRecordList", verifyToken, async (req, res) => {
  try {
    // 获取对象中的参数
    const { device_number, area, depart, date, page, pageSize } = req.query;
    let pages = page || 1;
    let pageSizes = pageSize || 10;
    let start = (pages - 1) * pageSizes;
    let end = pageSizes;
    let where = "";
    // 判断是否有设备编号
    if (device_number) {
      where += ` AND number_machine = '${device_number}'`;
    }
    // 判断是否有巡检区域
    if (area) {
      // 去掉area中间的-
      const area1 = area.replace(/-/g, "");
      where += ` AND location = '${area1}'`;
    }
    // 判断是否有巡检部门
    if (depart) {
      where += ` AND department = '${depart}'`;
    }
    // 判断是否有巡检时间,有则根据巡检时间查询
    if (date) {
      where += ` AND create_time between '${date[0]} 00:00:00' and '${date[1]} 23:59:59'`;
    }
    // 根据条件查询数据库中的巡检记录列表
    const sql = `SELECT * FROM check_equitment WHERE 1 = 1 ${where} ORDER BY create_time DESC LIMIT ${start}, ${end}`;
    const result = await query(sql);
    // 根据条件查询数据库中的巡检记录列表的总条数
    const sql1 = `SELECT COUNT(*) AS total FROM check_equitment WHERE 1 = 1 ${where}`;
    const total = await query(sql1);
    if (result.length > 0) {
      for (let i = 0; i < result.length; i++) {
        result[i].create_time = moment(result[i].create_time).format(
          "YYYY-MM-DD HH:mm:ss"
        );
      }
      return res.Success("获取成功", { list: result, total: total[0].total });
    }
    return res.Success("获取成功", { list: [], total: 0 });
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理退出登录接口
router.post("/v1/logout", verifyToken, async (req, res) => {
  try {
    // 获取对象中的参数
    const { user } = req;
    const uuid = user.userId;
    // 根据uuid删除redis中的token，和用户信息
    // 清除redis中的token
    client.del(`userToken:${uuid}`);
    // 清除redis中的用户信息
    client.del(`userInfo:${uuid}`);
    return res.Success("退出成功");
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理添加角色接口
router.post("/v1/addRole", verifyToken, async (req, res) => {
  try {
    // 获取对象中的参数
    const { role_name, role_desc, id } = req.body;
    const date = moment().format("YYYY-MM-DD HH:mm:ss");
    // joi验证参数
    const schema = Joi.object({
      role_name: Joi.string().required().error(new Error("角色名称不能为空")),
      role_desc: Joi.string().required().error(new Error("角色描述不能为空")),
    });
    const { error } = schema.validate({ role_name, role_desc });
    if (error) {
      return res.Error(error.message);
    }
    // 判断是否有id，有则修改角色信息，没有则添加角色
    if (id) {
      // 根据id查询数据库中是否存在该角色
      const sql1 = `SELECT * FROM role WHERE id = ${id}`;
      const result1 = await query(sql1);
      if (result1.length === 0) {
        return res.Error("该角色不存在");
      }
      // 根据id修改角色信息
      const sql =
        "UPDATE role SET role_name = ?, role_desc = ?, update_time = ? WHERE id = ?";
      const values = [role_name, role_desc, date, id];
      const result = await query(sql, values);
      if (result.affectedRows > 0) {
        return res.Success("修改成功");
      }
      return res.Error("修改失败");
    }
    // 根据角色名称查询数据库中是否存在该角色
    const sql = `SELECT * FROM role WHERE role_name = '${role_name}'`;
    const result = await query(sql);
    if (result.length > 0) {
      return res.Error("该角色已存在");
    }
    // 根据角色名称和角色描述添加角色
    const sql1 = `INSERT INTO role (role_name, role_desc, create_time, update_time) VALUES (?, ?, ?, ?)`;
    const result1 = await query(sql1, [role_name, role_desc, date, date]);
    if (result1.affectedRows > 0) {
      return res.Success("添加成功");
    }
    return res.Error("添加失败");
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理获取角色列表接口
router.get("/v1/getRoleList", verifyToken, async (req, res) => {
  try {
    // 获取对象中的参数
    const { name, page, pageSize } = req.query;
    let pages = page || 1;
    let pageSizes = pageSize || 10;
    let start = (pages - 1) * pageSizes;
    let end = pageSizes;
    let where = "";
    // 判断是否有角色名称
    if (name) {
      where += ` AND role_name = '${name}'`;
    }
    // 根据条件查询数据库中的角色列表
    const sql = `SELECT * FROM role WHERE 1 = 1 ${where} ORDER BY create_time DESC LIMIT ${start}, ${end}`;
    const result = await query(sql);
    // 根据条件查询数据库中的角色列表的总条数
    const sql1 = `SELECT COUNT(*) AS total FROM role WHERE 1 = 1 ${where}`;
    const total = await query(sql1);
    if (result.length > 0) {
      for (let i = 0; i < result.length; i++) {
        result[i].create_time = moment(result[i].create_time).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        result[i].update_time = moment(result[i].update_time).format(
          "YYYY-MM-DD HH:mm:ss"
        );
      }
      return res.Success("获取成功", { list: result, total: total[0].total });
    }
    return res.Success("获取成功", { list: [], total: 0 });
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理删除角色接口
router.post("/v1/deleteRole", verifyToken, async (req, res) => {
  try {
    // 获取对象中的参数
    const { id } = req.body;
    // 根据id查询数据库中是否存在该角色
    const sql1 = `SELECT * FROM role WHERE id = ${id}`;
    const result1 = await query(sql1);
    if (result1.length === 0) {
      return res.Error("该角色不存在");
    }
    // 根据id删除角色
    const sql = `DELETE FROM role WHERE id = ${id}`;
    const result = await query(sql);
    if (result.affectedRows > 0) {
      return res.Success("删除成功");
    }
    return res.Error("删除失败");
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});
// 后台管理添加管理员接口
router.post("/v1/addAdmin", verifyToken, async (req, res) => {
  try {
    // 获取对象中的参数
    const { username, password, email, department, avatar, id } = req.body;
    const date = moment().format("YYYY-MM-DD HH:mm:ss");
    // joi验证参数
    const schema = Joi.object({
      username: Joi.string().required().error(new Error("用户名不能为空")),
      password: Joi.string().required().error(new Error("密码不能为空")),
      email: Joi.string().required().error(new Error("邮箱不能为空")),
      department: Joi.string().required().error(new Error("部门不能为空")),
      avatar: Joi.string().required().error(new Error("头像不能为空")),
      id: Joi.number().integer().error(new Error("id必须为整数")),
    });
    const { error } = schema.validate({
      username,
      password,
      email,
      department,
      avatar,
    });
    if (error) {
      return res.Error(error.message);
    }
    // 将密码进行加密
    const newPass = encryptPassword(password);
    if (id) {
      // 根据id查询数据库中是否存在该管理员
      const sql3 = `SELECT * FROM admin WHERE id = ${id}`;
      const result3 = await query(sql3);
      if (result3.length === 0) {
        return res.Error("该管理员不存在");
      }
      // 根据邮箱查询数据库中是否存在该管理员
      const sql1 = `SELECT * FROM admin WHERE email = '${email}' AND id != ${id}`;
      const result1 = await query(sql1);
      if (result1.length > 0) {
        return res.Error("该邮箱已被别人注册");
      }
      // 根据id修改管理员信息
      const sql2 = `UPDATE admin SET username = ?, password = ?, email = ?, department = ?, avatar = ?, update_time = ? WHERE id = ?`;
      const result2 = await query(sql2, [
        username,
        newPass,
        email,
        department,
        avatar,
        date,
        id,
      ]);
      if (result2.affectedRows > 0) {
        return res.Success("修改成功");
      }
      return res.Error("修改失败");
    }
    // 根据用户名查询数据库中是否存在该管理员
    const sql = `SELECT * FROM admin WHERE username = '${username}'`;
    const result = await query(sql);
    if (result.length > 0) {
      return res.Error("该管理员已存在");
    }
    // 根据邮箱查询数据库中是否存在该管理员
    const sql1 = `SELECT * FROM admin WHERE email = '${email}'`;
    const result1 = await query(sql1);
    if (result1.length > 0) {
      return res.Error("该邮箱已被别人注册");
    }
    // 添加管理员
    const sql2 = `INSERT INTO admin (username, password, email, department, avatar, create_time, update_time) VALUES (?, ?, ?, ?, ?, ?, ?)`;
    const result2 = await query(sql2, [
      username,
      newPass,
      email,
      department,
      avatar,
      date,
      date,
    ]);
    if (result2.affectedRows > 0) {
      return res.Success("添加成功");
    }
    return res.Error("添加失败");
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理获取管理员列表接口
router.get("/v1/getAdminList", verifyToken, async (req, res) => {
  try {
    // 获取对象中的参数
    const { keywords, department, type, status, page, pageSize } = req.query;
    let pages = page || 1;
    let pageSizes = pageSize || 10;
    let start = (pages - 1) * pageSizes;
    let end = pageSizes;
    let where = "";
    // 判断是否有关键字,有则根据邮箱和用户名模糊查询
    if (keywords) {
      where += ` AND (username LIKE '%${keywords}%' OR email LIKE '%${keywords}%')`;
    }
    // 判断是否有部门
    if (department) {
      where += ` AND department = '${department}'`;
    }
    // 判断是否有状态
    let status1 = "";
    if (status) {
      status === "正常" ? (status1 = 0) : (status1 = 1);
      where += ` AND status = '${status1}'`;
    }
    // 判断是否有类型
    if (type) {
      where += ` AND roles = '${type}'`;
    }
    // 根据条件查询数据库中的管理员列表,获取status不等于2的管理员
    const sql = `SELECT * FROM admin WHERE status != 2 ${where} ORDER BY create_time DESC LIMIT ${start}, ${end}`;
    const result = await query(sql);
    // 根据条件查询数据库中的管理员列表的总条数
    const sql1 = `SELECT COUNT(*) AS total FROM admin WHERE 1 = 1 ${where}`;
    const total = await query(sql1);
    if (result.length > 0) {
      return res.Success("获取成功", { list: result, total: total[0].total });
    }
    return res.Success("获取成功", { list: [], total: 0 });
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理删除管理员接口
router.post("/v1/deleteAdmins", verifyToken, async (req, res) => {
  try {
    // 获取对象中的参数
    const { ids } = req.body;
    // 校验参数
    const schema = Joi.object({
      ids: Joi.required().error(new Error("必要参数不能为空")),
    });
    const { error } = schema.validate({ ids });
    if (error) {
      return res.Error(error.message);
    }
    // 如果idList是字符串,则转换成数组
    let idArr = [];
    if (typeof ids === "string") {
      idArr.push(ids);
    } else {
      idArr = ids;
    }
    // 根据id删除管理员
    const sql = `DELETE FROM admin WHERE id IN (${idArr})`;
    const result = await query(sql);
    if (result.affectedRows > 0) {
      return res.Success("删除成功");
    }
    return res.Error("删除失败");
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理修改管理员状态接口
router.post("/v1/changeAdminStatus", verifyToken, async (req, res) => {
  try {
    // 获取对象中的参数
    const { id, status } = req.body;
    // 校验参数
    const schema = Joi.object({
      id: Joi.number().required().error(new Error("id不能为空")),
      status: Joi.number().required().error(new Error("状态不能为空")),
    });
    const { error } = schema.validate({ id, status });
    if (error) {
      return res.Error(error.message);
    }
    // 根据id修改管理员状态
    const sql = `UPDATE admin SET status = ? WHERE id = ?`;
    const result = await query(sql, [status, id]);
    if (result.affectedRows > 0) {
      return res.Success("修改用户状态成功");
    }
    return res.Error("修改失败");
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理管理员角色分配接口
router.post("/v1/dispatchAdminRole", verifyToken, async (req, res) => {
  try {
    // 获取对象中的参数
    const { id, role } = req.body;
    // 校验参数
    const schema = Joi.object({
      id: Joi.number().required().error(new Error("id不能为空")),
      role: Joi.string().required().error(new Error("角色不能为空")),
    });
    const { error } = schema.validate({ id, role });
    if (error) {
      return res.Error(error.message);
    }
    // 根据id修改管理员角色
    const sql = `UPDATE admin SET roles = ? WHERE id = ?`;
    const result = await query(sql, [role, id]);
    if (result.affectedRows > 0) {
      return res.Success("修改用户角色成功");
    }
    return res.Error("修改失败");
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理添加部门信息接口
router.post("/v1/addDepartmentInfo", verifyToken, async (req, res) => {
  try {
    // 获取对象中的参数
    const { name, depart_manager, email, depart_desc, id } = req.body;
    const date = moment().format("YYYY-MM-DD HH:mm:ss");
    // 校验参数
    const schema = Joi.object({
      name: Joi.string().required().error(new Error("部门名称不能为空")),
      depart_manager: Joi.string()
        .required()
        .error(new Error("部门负责人不能为空")),
      email: Joi.string().required().error(new Error("邮箱不能为空")),
      depart_desc: Joi.string().required().error(new Error("部门描述不能为空")),
      id: Joi.number().optional().allow(""),
    });
    const { error } = schema.validate({
      name,
      depart_manager,
      email,
      depart_desc,
    });
    if (error) {
      return res.Error(error.message);
    }
    // 如果存在id,则修改部门信息
    if (id) {
      const sql = `UPDATE department SET depart_name = ?, depart_manager = ?, email = ?, depart_desc = ?, update_time = ? WHERE id = ?`;
      const result = await query(sql, [
        name,
        depart_manager,
        email,
        depart_desc,
        date,
        id,
      ]);
      if (result.affectedRows > 0) {
        return res.Success("修改部门信息成功");
      }
      return res.Error("修改失败");
    }
    // 根据部门名称查询数据库中是否存在该部门
    const sql = `SELECT * FROM department WHERE depart_name = ?`;
    const result = await query(sql, [name]);
    if (result.length > 0) {
      return res.Error("该部门已存在");
    }
    // 添加到数据库中
    const sql1 = `INSERT INTO department(depart_name, depart_manager, email, depart_desc, create_time, update_time) VALUES(?, ?, ?, ?, ?, ?)`;
    const result1 = await query(sql1, [
      name,
      depart_manager,
      email,
      depart_desc,
      date,
      date,
    ]);
    if (result1.affectedRows > 0) {
      return res.Success("创建成功");
    }
    return res.Error("创建失败");
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理获取部门信息接口
router.get("/v1/getDepartmentInfoList", verifyToken, async (req, res) => {
  try {
    // 获取对象中的参数
    const { name, page, pageSize } = req.query;
    let pages = page || 1;
    let pageSizes = pageSize || 10;
    let start = (pages - 1) * pageSizes;
    let end = pageSizes;
    let where = "";
    if (name) {
      where += ` AND depart_name LIKE '%${name}%'`;
    }
    // 查询数据库中的部门信息
    const sql = `SELECT * FROM department WHERE 1 = 1 ${where} ORDER BY create_time DESC LIMIT ${start}, ${end}`;
    const result = await query(sql);
    // 查询数据库中的部门总数
    const sql1 = `SELECT COUNT(*) AS total FROM department WHERE 1 = 1 ${where}`;
    const result1 = await query(sql1);
    if (result.length > 0) {
      // 将result中的create_time和update_time时间戳转换成时间格式
      result.forEach((item) => {
        item.create_time = moment(item.create_time).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        item.update_time = moment(item.update_time).format(
          "YYYY-MM-DD HH:mm:ss"
        );
      });
      return res.Success("获取成功", {
        list: result,
        total: result1[0].total,
      });
    } else {
      return res.Success("获取成功", {
        list: [],
        total: 0,
      });
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理删除部门信息接口
router.post("/v1/deleteDepartmentInfo", verifyToken, async (req, res) => {
  try {
    // 获取对象中的参数
    const { id } = req.body;
    // 校验参数
    const schema = Joi.object({
      id: Joi.number().required().error(new Error("id不能为空")),
    });
    const { error } = schema.validate({ id });
    if (error) {
      return res.Error(error.message);
    }
    // 根据id删除部门信息
    const sql = `DELETE FROM department WHERE id = ?`;
    const result = await query(sql, [id]);
    if (result.affectedRows > 0) {
      return res.Success("删除成功");
    }
    return res.Error("删除失败");
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理添加一级菜单信息接口
router.post("/v1/addMenuInfo", verifyToken, async (req, res) => {
  try {
    // 获取对象中的参数
    const { title, router, icon, sort, status, id } = req.body;
    const date = moment().format("YYYY-MM-DD HH:mm:ss");
    // 校验参数
    const schema = Joi.object({
      title: Joi.string().required().error(new Error("菜单名称不能为空")),
      router: Joi.string().required().error(new Error("路由链接不能为空")),
      icon: Joi.string().required().error(new Error("图标不能为空")),
      sort: Joi.number().optional().allow(""),
      status: Joi.number().optional().allow(""),
      id: Joi.number().optional().allow(""),
    });

    const { error } = schema.validate({ title, router, icon, sort, status });
    if (error) {
      return res.Error(error.message);
    }
    // 如果存在id,则修改菜单信息
    if (id) {
      const sql = `UPDATE menu SET title = ?, router = ?, icon = ?, sort = ?, status = ?, update_time = ? WHERE id = ?`;
      const result = await query(sql, [
        title,
        router,
        icon,
        sort,
        status,
        date,
        id,
      ]);
      if (result.affectedRows > 0) {
        return res.Success("修改菜单信息成功");
      }
      return res.Error("修改失败");
    }
    // 根据菜单名称查询数据库中是否存在该菜单
    const sql = `SELECT * FROM menu WHERE title = ?`;
    const result = await query(sql, [title]);
    if (result.length > 0) {
      return res.Error("该菜单已存在");
    }
    // 添加到数据库中
    const sql1 = `INSERT INTO menu(title, router, icon, sort, status, create_time, update_time) VALUES(?, ?, ?, ?, ?, ?, ?)`;
    const result1 = await query(sql1, [
      title,
      router,
      icon,
      sort,
      status,
      date,
      date,
    ]);
    if (result1.affectedRows > 0) {
      return res.Success("创建成功");
    }
    return res.Error("创建失败");
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理获取菜单列表信息接口
router.get("/v1/getMenuInfoList", verifyToken, async (req, res) => {
  try {
    const { page = 1, pageSize = 10 } = req.query;
    const start = (page - 1) * pageSize;
    const end = pageSize;

    const [result, result1] = await Promise.all([
      query(`SELECT * FROM menu ORDER BY sort DESC LIMIT ${start}, ${end}`),
      query(`SELECT COUNT(*) AS total FROM menu`),
    ]);

    if (result.length > 0) {
      const subMenuPromises = result.map((item) => {
        const sql2 = `SELECT * FROM submenu WHERE menu_id = ?`;
        return query(sql2, [item.id]);
      });

      const subMenuResults = await Promise.all(subMenuPromises);

      result.forEach((item, index) => {
        item.create_time = moment(item.create_time).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        item.update_time = moment(item.update_time).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        item.subMenu = subMenuResults[index];
      });

      return res.Success("获取成功", {
        list: result,
        total: result1[0].total,
      });
    } else {
      return res.Success("获取成功", {
        list: [],
        total: 0,
      });
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理添加二级菜单信息接口
router.post("/v1/addSubMenuInfo", verifyToken, async (req, res) => {
  try {
    // 获取对象中的参数
    const { title, router, icon, sort, status, pid, id } = req.body;
    // 校验参数
    const schema = Joi.object({
      title: Joi.string().required().error(new Error("子菜单名称不能为空")),
      router: Joi.string().required().error(new Error("路由链接不能为空")),
      icon: Joi.string().required().error(new Error("图标不能为空")),
      sort: Joi.number().optional().allow(""),
      status: Joi.number().optional().allow(""),
      pid: Joi.number().required().error(new Error("父级菜单id不能为空")),
      id: Joi.number().optional().allow(""),
    });

    const { error } = schema.validate({
      title,
      router,
      icon,
      sort,
      status,
      pid,
      id,
    });
    if (error) {
      return res.Error(error.message);
    }
    // 如果存在menu_id,则修改菜单信息
    if (id) {
      const sql = `UPDATE submenu SET title = ?, router = ?, icon = ?, sort = ?, status = ? WHERE id = ?`;
      const result = await query(sql, [title, router, icon, sort, status, id]);
      if (result.affectedRows > 0) {
        return res.Success("修改子菜单信息成功");
      }
      return res.Error("修改失败");
    }
    // 查询数据库表submenu中是否存在该菜单
    const sql = `SELECT * FROM submenu WHERE title = ? AND menu_id = ?`;
    const result = await query(sql, [title, pid]);
    if (result.length > 0) {
      return res.Error("该子菜单已存在");
    }
    // 添加到数据库中
    const sql1 = `INSERT INTO submenu(title, router, icon, sort, status, menu_id) VALUES(?, ?, ?, ?, ?, ?)`;
    const result1 = await query(sql1, [title, router, icon, sort, status, pid]);
    if (result1.affectedRows > 0) {
      return res.Success("添加成功");
    }
    return res.Error("添加失败");
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理删除一级菜单信息接口
router.post("/v1/deleteSubMenuInfo", verifyToken, async (req, res) => {
  try {
    const { id } = req.body;
    // 校验参数
    const schema = Joi.object({
      id: Joi.number().required().error(new Error("参数校验不合法")),
    });

    const { error } = schema.validate({ id });
    if (error) {
      return res.Error(error.message);
    }
    // 根据id查询数据库中是否存在该菜单
    const sql = `SELECT * FROM menu WHERE id = ?`;
    const result = await query(sql, [id]);
    if (result.length === 0) {
      return res.Error("该菜单不存在");
    }
    // 删除数据库中的菜单
    const sql1 = `DELETE FROM menu WHERE id = ?`;
    const result1 = await query(sql1, [id]);
    if (result1.affectedRows > 0) {
      return res.Success("删除成功");
    }
    return res.Error("删除失败");
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理删除二级菜单信息接口
router.post("/v1/delChildMenus", verifyToken, async (req, res) => {
  try {
    const { id } = req.body;
    // 校验参数
    const schema = Joi.object({
      id: Joi.number().required().error(new Error("参数校验不合法")),
    });

    const { error } = schema.validate({ id });
    if (error) {
      return res.Error(error.message);
    }
    // 根据id查询数据库中是否存在该菜单
    const sql = `SELECT * FROM submenu WHERE id = ?`;
    const result = await query(sql, [id]);
    if (result.length === 0) {
      return res.Error("该子菜单不存在");
    }
    // 删除数据库中的菜单
    const sql1 = `DELETE FROM submenu WHERE id = ?`;
    const result1 = await query(sql1, [id]);
    if (result1.affectedRows > 0) {
      return res.Success("删除成功");
    }
    return res.Error("删除失败");
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理获取权限列表接口
router.get("/v1/getPermissionList", verifyToken, async (req, res) => {
  try {
    // 获取menu表中的数据
    const sql = `SELECT id, title, icon FROM menu ORDER BY sort ASC`;
    const result = await query(sql);
    // 根据result中的id查询submenu表中的数据
    const subMenuPromises = result.map((item) => {
      const sql1 = `SELECT id, title, icon FROM submenu WHERE menu_id = ?`;
      return query(sql1, [item.id]);
    });
    const subMenuResults = await Promise.all(subMenuPromises);
    // 将subMenuResults中的数据添加到result中
    result.forEach((item, index) => {
      item.children = subMenuResults[index];
    });
    if (result.length > 0) {
      return res.Success("获取成功", { list: result });
    }
    return res.Success("暂无数据");
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理添加配件分类接口
router.post("/v1/addPartsClassify", verifyToken, async (req, res) => {
  try {
    // 获取对象中的参数
    const { classifyName, firstClass, secondClass, id } = req.body;
    const date = moment().format("YYYY-MM-DD HH:mm:ss");
    // 校验参数
    const schema = Joi.object({
      classifyName: Joi.string()
        .required()
        .error(new Error("配件分类名称不能为空")),
      firstClass: Joi.string().required().error(new Error("一级分类不能为空")),
      secondClass: Joi.string().required().error(new Error("二级分类不能为空")),
      id: Joi.number().optional().allow(""),
    });
    const { error } = schema.validate({
      classifyName,
      firstClass,
      secondClass,
    });
    if (error) {
      return res.Error(error.message);
    }
    // 如果存在id,则修改配件分类信息
    if (id) {
      const sql = `UPDATE fitting_classify SET classify_name = ?, first_class = ?, second_class = ?, update_time = ? WHERE id = ?`;
      const result = await query(sql, [
        classifyName,
        firstClass,
        secondClass,
        date,
        id,
      ]);
      if (result.affectedRows > 0) {
        return res.Success("修改成功");
      }
      return res.Error("修改失败");
    }
    // 根据配件分类名称查询数据库中是否存在该配件分类
    const sql = `SELECT * FROM fitting_classify WHERE classify_name = ?`;
    const result = await query(sql, [classifyName]);
    if (result.length > 0) {
      return res.Error("该配件分类已存在");
    }
    // 添加到数据库中
    const sql1 = `INSERT INTO fitting_classify(classify_name, first_class, second_class, create_time, update_time) VALUES(?, ?, ?, ?, ?)`;
    const result1 = await query(sql1, [
      classifyName,
      firstClass,
      secondClass,
      date,
      date,
    ]);
    if (result1.affectedRows > 0) {
      return res.Success("添加成功");
    }
    return res.Error("添加失败");
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理获取分类列表接口
router.get("/v1/getPartsClassifyList", verifyToken, async (req, res) => {
  try {
    // 获取对象中的参数
    const { name, page, pageSize } = req.query;
    let pages = page || 1;
    let pageSizes = pageSize || 10;
    let start = (pages - 1) * pageSizes;
    let end = pageSizes;
    let where = "";
    if (name) {
      where += ` AND classify_name LIKE '%${name}%'`;
    }
    // 查询数据库中的分类信息
    const sql = `SELECT * FROM fitting_classify WHERE 1 = 1 ${where} ORDER BY create_time DESC LIMIT ${start}, ${end}`;
    const result = await query(sql);
    // 查询数据库中的分类总数
    const sql1 = `SELECT COUNT(*) AS total FROM fitting_classify WHERE 1 = 1 ${where}`;
    const result1 = await query(sql1);
    if (result.length > 0) {
      // 将result中的create_time和update_time时间戳转换成时间格式
      result.forEach((item) => {
        item.create_time = moment(item.create_time).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        item.update_time = moment(item.update_time).format(
          "YYYY-MM-DD HH:mm:ss"
        );
      });
      return res.Success("获取成功", {
        list: result,
        total: result1[0].total,
      });
    } else {
      return res.Success("获取成功", {
        list: [],
        total: 0,
      });
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后态管理配件分类删除接口
router.post("/v1/deleteOneclassify", verifyToken, async (req, res) => {
  try {
    // 获取对象中的参数
    const { id } = req.body;
    // 校验参数
    const schema = Joi.object({
      id: Joi.number().required().error(new Error("id不能为空")),
    });
    const { error } = schema.validate({ id });
    if (error) {
      return res.Error(error.message);
    }
    // 根据id查询数据库中是否存在该配件分类
    const sql = `SELECT * FROM fitting_classify WHERE id = ?`;
    const result = await query(sql, [id]);
    if (result.length === 0) {
      return res.Error("该配件分类不存在");
    }
    // 删除数据库中的配件分类
    const sql1 = `DELETE FROM fitting_classify WHERE id = ?`;
    const result1 = await query(sql1, [id]);
    if (result1.affectedRows > 0) {
      return res.Success("删除成功");
    }
    return res.Error("删除失败");
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理创建货架接口
router.post("/v1/addShelfInfo", verifyToken, async (req, res) => {
  try {
    // 获取对象中的参数
    const { name, department, type, avatar } = req.body;
    // 校验参数
    const schema = Joi.object({
      name: Joi.string().required().error(new Error("货架名称不能为空")),
      department: Joi.string().required().error(new Error("所属部门不能为空")),
      type: Joi.string().required().error(new Error("货架存储类型不能为空")),
      avatar: Joi.string().required().error(new Error("货架图片不能为空")),
    });
    const { error } = schema.validate({ name, department, type, avatar });
    if (error) {
      return res.Error(error.message);
    }
    // 根据货架名称查询数据库中是否存在该货架
    const sql = `SELECT * FROM shelf WHERE shelf_name = ? and department = ?`;
    const result = await query(sql, [name, department]);
    if (result.length > 0) {
      return res.Error("该货架已存在，请不要重复添加");
    }
    // 添加到数据库中
    const sql1 = `INSERT INTO shelf(shelf_name, department, type, images) VALUES(?, ?, ?, ?)`;
    const result1 = await query(sql1, [name, department, type, avatar]);
    if (result1.affectedRows > 0) {
      return res.Success("创建成功");
    }
    return res.Error("创建失败");
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理添加库位接口
router.post("/v1/addStorageInfo", verifyToken, async (req, res) => {
  try {
    // 获取对象中的参数
    const { name, area, depart } = req.body;
    // 校验参数
    const schema = Joi.object({
      name: Joi.string().required().error(new Error("库位编号不能为空")),
      area: Joi.string().required().error(new Error("所属货架不能为空")),
      depart: Joi.string().required().error(new Error("所属部门不能为空")),
    });
    const { error } = schema.validate({ name, area, depart });
    if (error) {
      return res.Error(error.message);
    }
    // 根据货架名称查询数据库中是否存在该货架
    const sql = `SELECT * FROM storage_area WHERE storage_number = ? and shelf = ? and department = ?`;
    const result = await query(sql, [name, area, depart]);
    if (result.length > 0) {
      return res.Error("该库位在当前货架下已存在，请不要重复添加");
    }
    // 添加到数据库中
    const sql1 = `INSERT INTO storage_area(storage_number, shelf, department) VALUES(?, ?, ?)`;
    const result1 = await query(sql1, [name, area, depart]);
    if (result1.affectedRows > 0) {
      return res.Success("添加成功");
    }
    return res.Error("添加失败");
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 获取各部门的货架列表
router.get("/v1/getShelfInfoList", verifyToken, async (req, res) => {
  try {
    // 从数据库中获取所有的货架信息
    const sql = `SELECT * FROM shelf`;
    const result = await query(sql);

    // 将result中的数据根据department进行分组，children为对应的shelf_name
    const groupedResult = result.reduce((acc, item) => {
      const { department, shelf_name } = item;
      if (!acc[department]) {
        acc[department] = {
          value: department,
          label: department,
          children: [],
        };
      }
      acc[department].children.push({
        value: shelf_name,
        label: shelf_name,
      });
      return acc;
    }, {});

    // 将对象转换为数组
    const formattedResult = Object.values(groupedResult);

    return res.Success("获取成功", formattedResult);
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 后台管理备件出库卡号识别
router.post("/v1/employeeCard", verifyToken, async (req, res) => {
  try {
    const { cardNumber } = req.body;
    // 校验参数
    const schema = Joi.object({
      cardNumber: Joi.string().required().error(new Error("卡号不能为空")),
    });
    const { error } = schema.validate({ cardNumber });
    if (error) {
      return res.Error(error.message);
    }
    // 根据卡号查询数据库中是否存在该卡号
    const sql = `SELECT gh FROM CSH_CARD_GH WHERE card_id = '${cardNumber}'`;
    const result = await fetchFlynit(sql);
    if (result.length > 0) {
      // 根据卡号查询数据库中user表中的姓名， 班次
      const sql1 = `SELECT username, shift, department, avatar, uuid FROM users WHERE uuid = ?`;
      const cardInfo = await query(sql1, result[0].gh);
      return res.Success("识别成功", cardInfo[0]);
    }
    return res.Error("识别失败");
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 根据部门获取部门下的货架列表
router.get("/v1/getStorageInfoList", verifyToken, async (req, res) => {
  try {
    // 获取部门名称
    const { department, pagenum, pagesize } = req.query;
    // 根据部门名称查询数据库中是否存在该部门
    const sql = `SELECT * FROM shelf WHERE department = ? LIMIT ${
      (pagenum - 1) * pagesize
    }, ${pagesize}`;
    const result = await query(sql, [department]);
    // 查询总数量
    const sql2 = `SELECT COUNT(*) AS total FROM shelf WHERE department = ?`;
    const resultTotal = await query(sql2, [department]);
    // 根据result中的department去表storage_area中查询该部门下的所有库位个数
    const sql1 = `SELECT shelf, COUNT(DISTINCT storage_number) AS count FROM storage_area WHERE department = ? GROUP BY shelf`;
    const result1 = await query(sql1, [department]);
    // 如果result不为空，将result1中的count赋值给result中的count
    if (result.length > 0) {
      result.forEach((item) => {
        const countValue = result1.find(
          (value) => value.shelf === item.shelf_name
        );
        item.count = countValue ? countValue.count : 0;
      });
      return res.Success("获取成功", {
        list: result,
        total: resultTotal[0].total,
      });
    }
    // 如果result为空，返回空列表和总数
    return res.Success("获取成功", {
      list: [],
      total: resultTotal[0].total,
    });
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 获取部门列表
router.get("/v1/getDepartmentList", verifyToken, async (req, res) => {
  try {
    // 查询数据库中的部门信息
    const sql = `SELECT depart_name FROM department`;
    const result = await query(sql);
    if (result.length > 0) {
      return res.Success("获取成功", result);
    }
    return res.Success("获取成功", []);
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 初始化存储类型
router.post("/v1/initStorageType", verifyToken, async (req, res) => {
  // 接收参数
  const { type } = req.body;
  try {
    // 验证参数
    const schema = Joi.object({
      type: Joi.string().required().error(new Error("类型不能为空")),
    });
    const { error } = schema.validate({ type });
    if (error) {
      return res.Error(error.message);
    }
    // 查询数据库中相同name的数据是否存在
    const sql = `SELECT * FROM init_type WHERE name = ?`;
    const result = await query(sql, [type]);
    if (result.length > 0) {
      return res.Error("该类型已存在");
    }
    // 插入数据
    const sql1 = `INSERT INTO init_type(name) VALUES(?)`;
    const result1 = await query(sql1, [type]);
    if (result1.affectedRows > 0) {
      return res.Success("添加成功");
    }
    return res.Error("添加失败");
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 获取存储类型
router.get("/v1/getStorageType", verifyToken, async (req, res) => {
  try {
    // 查询数据库中的部门信息
    const sql = `SELECT name FROM init_type`;
    const result = await query(sql);
    if (result.length > 0) {
      return res.Success("获取成功", result);
    }
    return res.Success("获取成功", []);
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 获取备件分类列表
router.get("/v1/getPartsClassify", verifyToken, async (req, res) => {
  try {
    // 查询数据库中的部门信息
    const sql = `SELECT classify_name, first_class, second_class FROM fitting_classify`;
    const result = await query(sql);
    if (result.length > 0) {
      let data = [];
      result.forEach((item) => {
        let classifyName = data.find((i) => i.value === item.classify_name);
        if (!classifyName) {
          classifyName = {
            value: item.classify_name,
            label: item.classify_name,
            children: [],
          };
          data.push(classifyName);
        }
        let firstClass = classifyName.children.find(
          (i) => i.value === item.first_class
        );
        if (!firstClass) {
          firstClass = {
            value: item.first_class,
            label: item.first_class,
            children: [],
          };
          classifyName.children.push(firstClass);
        }
        let secondClass = firstClass.children.find(
          (i) => i.value === item.second_class
        );
        if (!secondClass) {
          secondClass = { value: item.second_class, label: item.second_class };
          firstClass.children.push(secondClass);
        }
      });
      return res.Success("获取成功", data);
    }
    return res.Success("获取成功", []);
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 根据部门和货架名称获取库位列表
router.get("/v1/getStorageListByShelf", verifyToken, async (req, res) => {
  try {
    const { department, shelf } = req.query;
    const sql = `SELECT * FROM storage_area WHERE department = ? AND shelf = ?`;
    const result = await query(sql, [department, shelf]);
    return res.Success("获取成功", result);
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 初始化单位
router.post("/v1/initUnit", verifyToken, async (req, res) => {
  const { unit } = req.body;
  try {
    // 检查是否已经存在相同的单位
    const checkSql = `SELECT * FROM unit WHERE unit = ?`;
    const checkResult = await query(checkSql, [unit]);
    if (checkResult.length > 0) {
      return res.Error("该单位已存在", 400);
    }
    // 插入新的单位
    const sql = `INSERT INTO unit(unit) VALUES(?)`;
    const result = await query(sql, [unit]);
    return res.Success("添加成功", result);
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 获取单位列表
router.get("/v1/getUnitList", verifyToken, async (req, res) => {
  try {
    const sql = `SELECT * FROM unit`;
    const result = await query(sql);
    return res.Success("获取成功", result);
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 获取设备信息列表
router.get("/v1/getEquipmentList", verifyToken, async (req, res) => {
  try {
    const sql = `SELECT device_name, device_image FROM device_info GROUP BY device_name`;
    const result = await query(sql);
    return res.Success("获取成功", result);
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 备件入库
router.post("/v1/sparePartsIn", verifyToken, async (req, res) => {
  const { data } = req.body;
  const {
    part_name,
    part_number,
    category,
    part_img,
    stock_specific,
    relation_device,
    specific,
    quantity,
    unit,
    amount,
    currency,
    shelf_name,
    storage_number,
    department,
    supplier,
    safe_stock,
    warn_stock,
    notification,
    remark,
  } = data;

  try {
    const user = req.user;
    const create_time = moment().utcOffset(7).format("YYYY-MM-DD HH:mm:ss");
    // 生成入库单号， 规则为TZML+年月日+8位随机数
    const orderNumber = `TZML${moment().format("YYYYMMDD")}${Math.floor(
      Math.random() * 100000000
    )
      .toString()
      .padStart(8, "0")}`;

    const insertStockInSql = `INSERT INTO stock_in(stock_number, part_name, part_number, category, part_img, stock_specific, relation_device, specifics, quantity, unit, amount, currency, shelf_name, storage_number, department, supplier, safety_stock, warn_stock, notification, operator, create_time, remarks) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;
    const stockInResult = await query(insertStockInSql, [
      orderNumber,
      part_name,
      part_number,
      category,
      part_img,
      stock_specific,
      relation_device,
      specific,
      quantity,
      unit,
      amount,
      currency,
      shelf_name,
      storage_number,
      department,
      supplier,
      safe_stock,
      warn_stock,
      notification,
      user.username,
      create_time,
      remark,
    ]);

    // Check if the stockInResult is valid and has affected rows
    if (stockInResult && stockInResult.affectedRows > 0) {
      const checkInventorySql = `SELECT * FROM inventory WHERE part_number = ? AND specifics = ? AND category = ? AND unit = ? AND shelf_name = ?`;
      const inventoryResult = await query(checkInventorySql, [
        part_number,
        specific,
        category,
        unit,
        shelf_name,
      ]);

      const currentTime = moment().utcOffset(7).format("YYYY-MM-DD HH:mm:ss");

      if (inventoryResult.length > 0) {
        const updateInventorySql = `
          UPDATE inventory 
          SET stock_quantity = stock_quantity + ?, last_updated = ? 
          WHERE part_number = ? AND specifics = ? AND category = ? AND unit = ? AND shelf_name = ?
        `;
        await query(updateInventorySql, [
          quantity,
          currentTime,
          part_number,
          specific,
          category,
          unit,
          shelf_name,
        ]);
      } else {
        const insertInventorySql = `
          INSERT INTO inventory(part_name, part_number, specifics, category, stock_quantity, unit, shelf_name, last_updated, department)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;
        await query(insertInventorySql, [
          part_name,
          part_number,
          specific,
          category,
          quantity,
          unit,
          shelf_name,
          currentTime,
          department,
        ]);
      }
      return res.Success("入库成功", stockInResult);
    } else {
      return res.Error("入库失败", 400);
    }
  } catch (error) {
    console.error("SQL Error: ", error);
    return res.Error("服务器异常: " + error.message, 500);
  }
});

// 获取备件类型列表
router.get("/v1/getPartsTypeList", verifyToken, async (req, res) => {
  try {
    const sql = `SELECT second_class FROM fitting_classify GROUP BY second_class`;
    const result = await query(sql);
    return res.Success("获取成功", result);
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 获取配件列表
// 获取备件类型列表
router.get("/v1/getPartsTypeList", verifyToken, async (req, res) => {
  try {
    const sql = `SELECT second_class FROM fitting_classify GROUP BY second_class`;
    const result = await query(sql);
    return res.Success("获取成功", result);
  } catch (error) {
    return res.Error(`服务器异常: ${error.message}`, 500);
  }
});

// 获取配件列表
router.get("/v1/getPartsList", verifyToken, async (req, res) => {
  try {
    const { page, pageSize, keywords, type, department } = req.query;

    // 验证参数
    const schema = Joi.object({
      page: Joi.number().required().error(new Error("页码不能为空")),
      pageSize: Joi.number().required().error(new Error("页码不能为空")),
    });

    const { error } = schema.validate({ page, pageSize });
    if (error) {
      return res.Error(error.message);
    }

    // 基础 SQL 语句
    let sql = `SELECT * FROM inventory WHERE 1 = 1`;
    const params = [];

    // 条件查询
    if (keywords) {
      sql += ` AND (part_name LIKE ? OR part_number LIKE ?)`;
      params.push(`%${keywords}%`, `%${keywords}%`);
    }
    if (type) {
      sql += ` AND category = ?`;
      params.push(type);
    }
    if (department) {
      sql += ` AND department = ?`;
      params.push(department);
    }

    // 分页
    sql += ` LIMIT ?, ?`;
    params.push((parseInt(page) - 1) * parseInt(pageSize), parseInt(pageSize));
    // 执行查询
    const result = await query(sql, params);

    // 获取相关配件的详细信息
    if (result.length > 0) {
      const partNumbers = result.map((item) => item.part_number);
      const stockInSql = `SELECT * FROM stock_in WHERE part_number IN (?)`;
      const stockInResult = await query(stockInSql, [partNumbers]);

      result.forEach((item) => {
        const stockInfo =
          stockInResult.find((i) => i.part_number === item.part_number) || {};
        item.partImg = stockInfo.part_img || null;
        item.amount = stockInfo.amount || 0;
        item.storage_number = stockInfo.storage_number || null;
        item.safety_stock = stockInfo.safety_stock || null;
        item.warn_stock = stockInfo.warn_stock || null;
        item.relation_device = stockInfo.relation_device || null;
      });
    }

    // 获取总条数
    let totalSql = `SELECT COUNT(*) AS total FROM inventory WHERE 1 = 1`;
    const totalParams = [];
    if (keywords) {
      totalSql += ` AND (part_name LIKE ? OR part_number LIKE ?)`;
      totalParams.push(`%${keywords}%`, `%${keywords}%`);
    }
    if (type) {
      totalSql += ` AND category = ?`;
      totalParams.push(type);
    }
    if (department) {
      totalSql += ` AND department = ?`;
      totalParams.push(department);
    }

    const totalResult = await query(totalSql, totalParams);

    // 返回结果
    return res.Success("获取成功", {
      list: result,
      total: totalResult[0]?.total || 0,
    });
  } catch (error) {
    console.error("Error:", error);
    return res.Error(`服务器异常: ${error.message}`, 500);
  }
});

// 获取备件入库单列表
router.get("/v1/getPartsInList", verifyToken, async (req, res) => {
  try {
    // 接收参数
    const {
      page,
      pageSize,
      stock_number,
      keywords,
      department,
      start_date,
      end_date,
    } = req.query;
    // 验证参数
    const schema = Joi.object({
      page: Joi.number().required().error(new Error("页码不能为空")),
      pageSize: Joi.number().required().error(new Error("页码不能为空")),
    });
    const { error } = schema.validate({ page, pageSize });
    if (error) {
      return res.Error(error.message);
    }
    let sql = `SELECT * FROM stock_in WHERE 1 = 1`;
    const params = [];
    if (stock_number) {
      sql += ` AND stock_number = ?`;
      params.push(stock_number);
    }
    if (keywords) {
      sql += ` AND (part_name LIKE ? OR part_number LIKE ?)`;
      params.push(`%${keywords}%`, `%${keywords}%`);
    }
    if (department) {
      sql += ` AND department = ?`;
      params.push(department);
    }
    if (start_date && end_date) {
      sql += ` AND create_time BETWEEN ? AND ?`;
      params.push(start_date, end_date);
    }
    sql += ` LIMIT ?, ?`;
    params.push((parseInt(page) - 1) * parseInt(pageSize), parseInt(pageSize));
    const result = await query(sql, params);
    // 将result中的create_time转换为YYYY-MM-DD HH:mm:ss
    result.forEach((item) => {
      item.create_time = moment(item.create_time).format("YYYY-MM-DD HH:mm:ss");
    });
    // 获取总条数
    let totalSql = `SELECT COUNT(*) AS total FROM stock_in WHERE 1 = 1`;
    const totalParams = [];
    if (stock_number) {
      totalSql += ` AND stock_number = ?`;
      totalParams.push(stock_number);
    }
    const totalResult = await query(totalSql, totalParams);
    return res.Success("获取成功", {
      list: result,
      total: totalResult[0]?.total || 0,
    });
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 根据入库单号删除入库单， 然后根据入库单中part_number的数量去库存表中减去对应的数量
router.post("/v1/deleteStockIn", verifyToken, async (req, res) => {
  try {
    // 接收参数
    const { part_number, stock_number, quantity } = req.body;
    // 校验参数
    const schema = Joi.object({
      stock_number: Joi.string().required().messages({
        "any.required": "入库单号不能为空",
        "string.base": "入库单号必须是字符串",
      }),
      part_number: Joi.string().required().messages({
        "any.required": "配件编号不能为空",
        "string.base": "配件编号必须是字符串",
      }),
      quantity: Joi.number().required().messages({
        "any.required": "数量不能为空",
        "number.base": "数量必须是数字",
      }),
    });
    const { error } = schema.validate({ stock_number, part_number, quantity });
    if (error) {
      return res.Error(error.message);
    }
    // 根据入库单号删除入库单
    const deleteSql = `DELETE FROM stock_in WHERE stock_number = ?`;
    const deleteResult = await query(deleteSql, [stock_number]);
    if (deleteResult.affectedRows > 0) {
      // 根据入库单中part_number的数量去库存表中减去对应的数量
      const updateSql = `UPDATE inventory SET stock_quantity = stock_quantity - ? WHERE part_number = ?`;
      const updateResult = await query(updateSql, [quantity, part_number]);
      if (updateResult.affectedRows > 0) {
        return res.Success("删除成功", deleteResult);
      } else {
        return res.Error("删除失败");
      }
    } else {
      return res.Error("删除失败");
    }
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 根据备件类型获取备件列表
router.get("/v1/getPartsListByType", verifyToken, async (req, res) => {
  const { type, page, pageSize } = req.query;
  try {
    // 验证参数
    const schema = Joi.object({
      type: Joi.string().required().error(new Error("备件类型不能为空")),
      page: Joi.number()
        .integer()
        .min(1)
        .required()
        .error(new Error("页码不能为空且必须为正整数")),
      pageSize: Joi.number()
        .integer()
        .min(1)
        .required()
        .error(new Error("页大小不能为空且必须为正整数")),
    });
    const { error } = schema.validate({ type, page, pageSize });
    if (error) {
      return res.Error(error.message);
    }

    // 确保分页参数为整数
    const offset = (parseInt(page, 10) - 1) * parseInt(pageSize, 10);
    const limit = parseInt(pageSize, 10);

    const sql = `
  SELECT 
    inventory.*, 
    stock_in.*
  FROM 
    inventory 
  JOIN 
    (
      SELECT 
        part_number, 
        department,
        part_img,
        storage_number,
        unit,
        specifics,
        MIN(id) as min_id
      FROM 
        stock_in
      GROUP BY 
        part_number, 
        department
    ) as stock_in 
  ON 
    inventory.part_number = stock_in.part_number 
    AND inventory.department = stock_in.department
  WHERE 
    inventory.category = ?
  LIMIT ?, ?`;

    const result = await query(sql, [type, offset, limit]);

    // 获取总条数
    const totalSql = `SELECT COUNT(*) AS total FROM inventory WHERE category = ?`;
    const totalResult = await query(totalSql, [type]);

    return res.Success("获取成功", {
      list: result,
      total: totalResult[0]?.total || 0,
    });
  } catch (error) {
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 配件出库
router.post("/v1/sparePartsOut", verifyToken, async (req, res) => {
  const { data, userinfo } = req.body;
  console.log(userinfo);
  try {
    // 获取当前用户信息
    const user = req.user;
    // 使用事务处理批量出库
    await query("START TRANSACTION");

    for (const item of data) {
      // 根据part_number查询库存表中的信息
      const sql = `SELECT * FROM inventory WHERE part_number = ?`;
      const result = await query(sql, [item.part_number]);

      if (result.length === 0) {
        await query("ROLLBACK");
        return res.Error(`库存中未找到配件编号: ${item.part_number}`);
      }

      // 如果data.destiniation为其他，则需要判断data.other是否为空
      if (!item.destiniation || item.destiniation === "") {
        await query("ROLLBACK");
        return res.Error("请选择使用用途");
      }

      // 如果出库数量大于库存数量，则出库失败
      if (item.quantity > result[0].stock_quantity) {
        await query("ROLLBACK");
        return res.Error(`库存中配件数量不足: ${item.part_number}`);
      }

      // 根据part_number查询入库信息
      const sql3 = `SELECT * FROM stock_in WHERE part_number = ?`;
      const result3 = await query(sql3, [item.part_number]);

      // 存储出库信息
      const sql1 = `INSERT INTO stockout (part_number, part_name, department, category, quantity, unit, storage_number, employer_id, employer_name, operator, create_time, destiniation) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;
      await query(sql1, [
        item.part_number,
        item.part_name,
        userinfo[0].department,
        result[0].category,
        item.quantity,
        result3[0].unit,
        result3[0].storage_number,
        userinfo[0].uuid,
        userinfo[0].username,
        user.username,
        moment().utcOffset(7).format("YYYY-MM-DD HH:mm:ss"),
        item.destiniation,
      ]);

      // 根据part_number去库存表中减去对应的数量
      const sql2 = `UPDATE inventory SET stock_quantity = stock_quantity - ? WHERE part_number = ?`;
      const result2 = await query(sql2, [item.quantity, item.part_number]);

      if (result2.affectedRows === 0) {
        await query("ROLLBACK");
        return res.Error(
          `出库失败，库存不足或配件编号: ${item.part_number} 不存在`
        );
      }
    }

    await query("COMMIT");
    // 发送邮件
    const msg = "Flyknit设备管家-备件出库通知";
    const recipients = [
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
    ];
    await sendEmailsToMultipleRecipients(recipients, msg);
    return res.Success("出库成功");
  } catch (error) {
    await query("ROLLBACK");
    return res.Error("服务器异常" + error.message, 500);
  }
});

// 导出路由
module.exports = router;
