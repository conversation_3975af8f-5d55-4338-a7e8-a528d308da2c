<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>IT年终报告</title>
        <link rel="stylesheet"
            href="./index.css">
        <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            color: #333;
            background-color: #e5e5e5;
            box-sizing: border-box;
            overflow-x: hidden;
        }
        .container {
            width: 60%;
            margin: 20px auto;
            padding: 20px 50px;
            border: 1px solid #eee;
            background-color: #feffff;
            box-sizing: border-box;
            overflow-x: hidden;
            border-radius: 15px;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h1 {
            margin: 0;
            font-size: 2vw; /* Use viewport width for responsive font size */
        }
        .chart {
            width: 100%;
            height: 55vh; /* Use viewport height for responsive chart size */
            margin-top: 5vh; /* Use viewport height for responsive margin */
            padding: 2rem;
            box-sizing: border-box;
            overflow: hidden;
        }
        .data-title {
            margin-top: 5vh; /* Use viewport height for responsive margin */
            font-size: 1.5rem;
            background: linear-gradient(to right, #fff, #318CE7);
            border-radius: 10px 10px 0 0;
            padding: 10px 0;
        }
        .report-title {
            font-size: 1.5rem;
            margin: 0 auto;
            font-size: 2rem;
            font-weight: bold;
            letter-spacing: 0.2rem;
        }
        .left-title {
           display: flex;
           justify-content: flex-start;
           align-items: center;
           width: 100%;
        }
        .system-name {
            font-size: 1.2rem;
            font-weight: bold;
            letter-spacing: 0.2rem;
            padding: 1rem 0;
        }
        .system-description {
            font-size: 1rem;
            letter-spacing: 0.2rem;
            padding: 1rem 0;
            text-indent: 2rem;
        }
        .start-content {
            font-size: 1rem;
            letter-spacing: 0.2rem;
            padding: 1rem 0;
        }
        h2 {
            font-size: 1.3rem;
            font-weight: bold;
            letter-spacing: 0.2rem;
            padding: 0.5rem 0;
        }
        h3 {
            font-size: 1rem;
            font-weight: normal;
            letter-spacing: 0.2rem;
            padding: 0.5rem 0;
            line-height: 2;
            text-indent: 2rem;
        }
        .scroll-to-top {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: #318CE7;
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            font-size: 24px;
            cursor: pointer;
            display: none;
            justify-content: center;
            align-items: center;
        }
        .report-footer {
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            align-items: flex-end;
            width: 100%;
        }
        @media (max-width: 768px) {
            .container {
                width: 95%;
                padding: 10px;
            }
            .header h1 {
                font-size: 4vw; /* Adjust font size for smaller screens */
            }
            .chart {
                height: 40vh; /* Adjust chart height for smaller screens */
            }
        }
    </style>
        <script src="./vue.js"></script>
        <script src="./lib.js"></script>
        <script src="./echart.js"></script>
    </head>
    <body>
        <div id="app" class="container"
            v-loading.fullscreen.lock="fullscreenLoading">
            <div class="header">
                <h1>2024年IT年终报告</h1>
            </div>
            <!-- 前序 -->
            <div class="start-content">
                <h2>
                    尊敬的领导:
                </h2>
                <h3>

                    2024年，IT部门秉承高效服务、技术赋能的原则，圆满完成了年度目标。硬件方面，共计完成设备维修和更换任务超过539次，其中织造、整理、检验、打样等重点车间设备维护保障率达到98%以上。新增服务器3台，新增网络布线超700米，安装交换机和路由器设备10余台，系统升级以及老化主机更换硬盘升级50多台，为企业的信息化运营提供了坚实的硬件基础。

                    软件开发方面，成功上线了打样巡检系统、样品测试系统、设备保养系统等多个关键业务平台，车间新增功能需求10项以上，BUG修复3次，系统稳定性达98%，数据安全性提升至99%。此外，研发了2个大屏可视化看板，进一步提升生产监控效率。

                    这一年，我们积极响应企业数字化转型需求，不断优化资源配置，提升员工技能水平，同时通过IT设备保养维护、数据备份等措施，有效降低了系统宕机率和硬件故障率，为各部门提供了有力支持。

                    未来，我们将继续以提升业务效率和信息化水平为核心，探索新技术应用，为公司发展保驾护航。感谢领导的支持与指导，IT部门将在新的一年再接再厉，为公司创造更大价值！

                    以下是一些数据统计，是IT部门在日常工作中的一些数据统计，请领导审阅。
                </h3>
            </div>
            <div class="report-title">一、IT硬件</div>
            <div class="data-title">各部门硬件维修统计</div>
            <el-table :data="repairData" style="width: 100%; margin-top: 20px;"
                stripe>
                <el-table-column prop="department" label="部门"
                    width="120"></el-table-column>
                <el-table-column prop="total_repairs" label="总维修次数"
                    align="center"
                    width="100"></el-table-column>
                <el-table-column prop="monitor" label="监控" align="center"
                    width="100"></el-table-column>
                <el-table-column prop="new_computer" label="主机安装"
                    align="center"
                    width="100"></el-table-column>
                <el-table-column prop="computer_repair" label="电脑维修"
                    align="center"
                    width="100"></el-table-column>
                <el-table-column prop="monitor_repair" label="显示器"
                    align="center"
                    width="100"></el-table-column>
                <el-table-column prop="scale_repair" label="电子称" align="center"
                    label="电子称"></el-table-column>
                <el-table-column prop="scanner_repair" align="center"
                    label="扫描仪"></el-table-column>
                <el-table-column prop="projector" align="center"
                    label="投影仪"></el-table-column>
                <el-table-column prop="print" align="center"
                    label="打印机"></el-table-column>
                <el-table-column prop="telephone" align="center"
                    label="电话机"></el-table-column>
                <el-table-column prop="switch" align="center"
                    label="交换机"></el-table-column>
                <el-table-column prop="access_control" align="center"
                    width="120"
                    label="门禁控制器"></el-table-column>
                <el-table-column prop="camera_region" align="center" width="120"
                    label="监控电源盒"></el-table-column>
                <el-table-column prop="wifi_ap" align="center"
                    label="无线AP"></el-table-column>
                <el-table-column prop="wangxian" align="center"
                    label="网络布线"></el-table-column>
                <el-table-column prop="poe" align="center" width="120"
                    label="POE电源盒"></el-table-column>
                <el-table-column prop="hard_drive" align="center"
                    label="电脑硬盘"></el-table-column>
                <el-table-column prop="upgrade_win10" align="center" width="120"
                    label="升级win10"></el-table-column>
                <el-table-column prop="upgrade_win11" align="center" width="120"
                    label="升级win11"></el-table-column>
            </el-table>
            <div class="data-title">数据可视化统计</div>
            <div id="department-repairs-chart" class="chart"></div>
            <div id="equipment-repairs-chart" class="chart"></div>
            <div id="daily-issues-chart" class="chart"></div>
            <div id="information-security-chart" class="chart"></div>
            <div style="display: flex; flex-direction: column;">
                <div class="data-title">2024IT部门新增采购设备及配件清单数量统计</div>
                <el-table :data="newEquipmentData"
                    style="width: 100%; margin-top: 20px;" stripe>
                    <el-table-column prop="name" label="设备名称"></el-table-column>
                    <el-table-column prop="value" label="数量"></el-table-column>
                    <el-table-column prop="location"
                        label="摆放位置"></el-table-column>
                </el-table>
                <div id="new-equipment-chart" class="chart"></div>
                <div class="report-title left-title">二、IT软件设计开发与运维</div>
                <div id="new-systems-chart" class="chart"></div>
            </div>
            <div class="system-detail">
                <div class="data-title">各系统开发完成进度简介</div>
                <div class="system-detail-content">
                    <div class="system-item" v-for="(item, index) in systemList"
                        :key="item">
                        <div class="system-description">
                            <div class="system-name">{{index+1}}、{{ item.name
                                }}</div>
                            <div class="system-description">{{ item.description
                                }}</div>
                            <el-image v-for="image in item.imageList"
                                :src="image"
                                :key="image"
                                style="width: 100%; height: 100%;"></el-image>
                        </div>
                    </div>
                </div>
            </div>
            <div class="report-title">2025年工作展望和计划</div>
            <div class="start-content">
                <h3>
                    展望2025年，IT部门将继续致力于提升设备管理流程和系统设计。我们计划进一步完善设备管理系统，确保设备的采购、安装、保养、维修和报废流程更加高效和透明，
                    设备巡检系统功能重构，实现工单化巡检，
                    新增设备盘点，做到实物与台账相符。同时，我们将优化现有系统的功能，提升用户体验和系统稳定性。

                    此外，我们将积极探索人工智能技术在生产数据分析中的应用。通过引入AI技术，我们希望能够更准确地预测设备故障，优化维护计划，提升生产效率。AI技术还将帮助我们更好地分析生产数据，发现潜在问题并提出改进建议，从而为企业的数字化转型提供有力支持。

                    我们相信，通过不断创新和优化，IT部门将在2025年为公司创造更大的价值。感谢领导的支持与指导，我们将继续努力，为公司的发展保驾护航！
                </h3>
            </div>
            <div class="report-footer">
                <h4>报告人: 杨晓伟</h4>
                <h4>报告日期: <span id="report-date"></span></h4>
            </div>
        </div>
        <button class="scroll-to-top" @click="scrollToTop">↑</button>
        <script>
        new Vue({
            el: '#app',
            data: {
                systemList: [
                    { name: '翻改系统', description: '翻改系统是用于车间翻改鞋面的系统，该套系统跟目前在用版本对比， 主要在于信息的全面性，数据的实时性传输，通知、计划排单等，目前完成度90%，预计将于2025前上年度上线。', 
                    imageList: ['http://*************:8001/b3c6d65f55411091770bb611acbca718.png', 'http://*************:8001/b17b3305cd1cf4598dae241694599579.png', 'http://*************:8001/78be5aba8664507b5d0a741ba1c89c68.png'] },
                    { name: '织造车间设备保养系统', description: '设备保养系统是用于工厂设备保养的系统，主要用于车间设备的采购，安装验收，保养，维修，报废等信息记录， 以及设备配件的关联，做设备运维数据分析，成本分析等， 目前完成度90%，预计将于2025前下半年上线使用， 部分功能将于年后上线使用，如备件管理，设备管理模块。', 
                    imageList: ['http://*************:8001/7d74aaa1084e7d77b00b712cf9e3d75e.png','http://*************:8001/569450e9b6dba49d401b5fff5bf27539.png','http://*************:8001/711d3904d06fde988c7fcc8c439998de.png'] },
                    { name: '打样车间巡检系统', description: '打样车间巡检系统是用于打样车间巡检的系统，主要用于打样车间鞋面的数据巡检，目前自上线以来， 运行稳定， 数据准确， 未有异常BUG出现， 维护升级3次。', 
                    imageList: ['http://*************:8001/914b8c30f39010d582d3120898c99c75.png', 'http://*************:8001/2b7f8c7691b506b520eaf8e90fa7a741.png'] },
                    { name: '样品测试系统', description: '样品测试系统是用于样品测试产品记录全流程操作的系统，目前处于上线测试阶段，目前未收到测试BUG反馈， 运行稳定。整套系统流程，完全按照打样间需求开发。后期还将新增产品管理模块，以及毛光坯缩率数据分析模块。', 
                    imageList: ['http://*************:8001/f21e11d343081c210355ad67d6330189.png', 'http://*************:8001/ea0f735887bdbec47458012b259157d5.png', 'http://*************:8001/e0c2f70426f34eda92484def988b4deb.png'] },
                    {
                        name: '原料仓库温湿度可视化看板', description: '原料仓库温湿度可视化看板是用于原料仓库F丝房间温湿度监控的系统，主要用于原料仓库温湿度的监控，已上线稳定运行，可实时获取温湿度数据，并进行数据分析， 以及数据可视化。', 
                        imageList: ['http://*************:8001/90468edbe20d805a3ad33dfe2fbefb75.png']
                    }
                ],
                repairData: [
                    { department: '检验车间', total_repairs: 164, monitor: 1, new_computer: 12, computer_repair: 31, monitor_repair: 7, scale_repair: 3, scanner_repair: 15, projector: 0, print: 30, telephone: 3, switch: 0, access_control: 1, camera_region: 0, wifi_ap: 0, wangxian: 2, poe: 0, hard_drive: 17, upgrade_win10: 15, upgrade_win11: 2 },
                    { department: '织造车间', total_repairs: 55, monitor: 10, new_computer: 0, computer_repair: 12, monitor_repair: 0, scale_repair: 0, scanner_repair: 5, projector: 0, print: 0, telephone: 0, switch: 2, access_control: 1, camera_region: 5, wifi_ap: 0, wangxian: 5, poe: 0, hard_drive: 1, upgrade_win10: 1, upgrade_win11: 0 },
                    { department: '整理车间', total_repairs: 12, monitor: 2, new_computer: 0, computer_repair: 10, monitor_repair: 0, scale_repair: 2, scanner_repair: 3, projector: 0, print: 0, telephone: 0, switch: 0, access_control: 1, camera_region: 0, wifi_ap: 0, wangxian: 0, poe: 0, hard_drive: 2, upgrade_win10: 1, upgrade_win11: 1 },
                    { department: '办公室', total_repairs: 27, monitor: 0, new_computer: 7, computer_repair: 15, monitor_repair: 4, scale_repair: 0, scanner_repair: 0, projector: 1, print: 4, telephone: 0, switch: 3, access_control: 1, camera_region: 2, wifi_ap: 0, wangxian: 5, poe: 0, hard_drive: 4, upgrade_win10: 3, upgrade_win11: 1 },
                    { department: '打样间', total_repairs: 104, monitor: 5, new_computer: 4, computer_repair: 25, monitor_repair: 2, scale_repair: 2, scanner_repair: 2, projector: 0, print: 7, telephone: 3, switch: 1, access_control: 1, camera_region: 0, wifi_ap: 0, wangxian: 5, poe: 0, hard_drive: 8, upgrade_win10: 7, upgrade_win11: 1 },
                    { department: '人事行政', total_repairs: 17, monitor: 0, new_computer: 0, computer_repair: 4, monitor_repair: 2, scale_repair: 0, scanner_repair: 0, projector: 1, print: 3, telephone: 0, switch: 0, access_control: 1, camera_region: 0, wifi_ap: 1, wangxian: 2, poe: 0, hard_drive: 7, upgrade_win10: 7, upgrade_win11: 0 },
                    { department: '业务', total_repairs: 31, monitor: 0, new_computer: 4, computer_repair: 20, monitor_repair: 4, scale_repair: 0, scanner_repair: 0, projector: 0, print: 3, telephone: 0, switch: 0, access_control: 1, camera_region: 0, wifi_ap: 2, wangxian: 3, poe: 2, hard_drive: 5, upgrade_win10: 2, upgrade_win11: 3 },
                    { department: '打样原料仓库', total_repairs: 37, monitor: 2, new_computer: 0, computer_repair: 10, monitor_repair: 1, scale_repair: 0, scanner_repair: 0, projector: 1, print: 1, telephone: 0, switch: 1, access_control: 1, camera_region: 0, wifi_ap: 0, wangxian: 0, poe: 0, hard_drive: 2, upgrade_win10: 2, upgrade_win11: 0 },
                    { department: '大货原料仓库', total_repairs: 22, monitor: 0, new_computer: 0, computer_repair: 5, monitor_repair: 1, scale_repair: 0, scanner_repair: 0, projector: 0, print: 3, telephone: 0, switch: 1, access_control: 1, camera_region: 0, wifi_ap: 0, wangxian: 5, poe: 0, hard_drive: 2, upgrade_win10: 2, upgrade_win11: 0 },
                    { department: '五金仓库', total_repairs: 4, monitor: 1, new_computer: 0, computer_repair: 4, monitor_repair: 1, scale_repair: 0, scanner_repair: 0, projector: 0, print: 1, telephone: 0, switch: 0, access_control: 1, camera_region: 0, wifi_ap: 0, wangxian: 2, poe: 0, hard_drive: 1, upgrade_win10: 1, upgrade_win11: 0 },
                    { department: '食堂', total_repairs: 36, monitor: 5, new_computer: 0, computer_repair: 0, monitor_repair: 1, scale_repair: 0, scanner_repair: 0, projector: 0, print: 0, telephone: 1, switch: 0, access_control: 1, camera_region: 0, wifi_ap: 0, wangxian: 5, poe: 0, hard_drive: 0, upgrade_win10: 0, upgrade_win11: 0 },
                    { department: '包装间', total_repairs: 23, monitor: 0, new_computer: 0, computer_repair: 4, monitor_repair: 1, scale_repair: 5, scanner_repair: 0, projector: 0, print: 10, telephone: 1, switch: 0, access_control: 1, camera_region: 0, wifi_ap: 0, wangxian: 0, poe: 0, hard_drive: 4, upgrade_win10: 4, upgrade_win11: 0 },
                    { department: '成品仓库', total_repairs: 5, monitor: 0, new_computer: 0, computer_repair: 1, monitor_repair: 2, scale_repair: 0, scanner_repair: 0, projector: 0, print: 0, telephone: 0, switch: 0, access_control: 1, camera_region: 0, wifi_ap: 2, wangxian: 0, poe: 0, hard_drive: 2, upgrade_win10: 2, upgrade_win11: 0 },
                    { department: '化验室', total_repairs: 2, monitor: 0, new_computer: 0, computer_repair: 4, monitor_repair: 3, scale_repair: 0, scanner_repair: 0, projector: 0, print: 1, telephone: 0, switch: 0, access_control: 1, camera_region: 0, wifi_ap: 0, wangxian: 0, poe: 0, hard_drive: 1, upgrade_win10: 5, upgrade_win11: 0 },
                    { department: '总计', total_repairs: 539, monitor: 26, new_computer: 27, computer_repair: 146, monitor_repair: 27, scale_repair: 12, scanner_repair: 25, projector: 3, print: 62, telephone: 8, switch: 10, access_control: 13, camera_region: 7, wifi_ap: 5, wangxian: 31, poe: 2, hard_drive: 56, upgrade_win10: 52, upgrade_win11: 8 }
                ],
                hardwareData: [
                    { name: '打印机维修', value: 62 },
                    { name: '电脑系统升级', value: 60 },
                    { name: '更换扫描枪电池', value: 25 },
                    { name: '更换手持机', value: 20 },
                    { name: '手持机系统升级', value: 40 },
                    { name: '电话维修', value: 8 },
                    { name: '横机网络维修', value: 5 },
                    { name: '光纤收发器更换', value: 10 },
                    { name: '交换机维修更换', value: 10 },
                    { name: '对讲机更换', value: 25 },
                    { name: '显示器更换', value: 27 },
                    { name: '笔记本维修', value: 20 },
                    { name: '路由器安装', value: 8 },
                    { name: '监控摄像头更换', value: 26 },
                    { name: '网络安装布线', value: 31 },
                    { name: '更换存储硬盘HDD', value: 56 },
                    { name: '系统蓝屏', value: 20 },
                    { name: '打印机故障', value: 30 },
                    { name: '监控电源盒故障维修更换', value: 7 },
                    { name: '光纤跳线更换', value: 10 },
                    { name: '水晶头安装', value: 300 }
                ],
                softwareData: [
                    { name: '新开发系统', value: 4 },
                    { name: '新增需求', value: 10 },
                    { name: '修改BUG', value: 3 },
                    { name: '系统稳定性', value: 98 },
                    { name: '数据安全性与备份', value: 99 },
                    { name: '大屏可视化看板新增开发', value: 2 },
                    { name: '大屏可视化看板升级', value: 2 }
                ],
                newEquipmentData: [
                    { name: 'DELL服务器', value: 3, purchase_date: '2024-01-15', installation_date: '2024-01-20', location: 'IT总机房' },
                    { name: '电脑主机', value: 19, purchase_date: '2024-02-10', installation_date: '2024-02-15', location: '办公室' },
                    { name: '移动硬盘1T', value: 1, purchase_date: '2024-03-05', installation_date: '2024-03-10', location: '办公室' },
                    { name: '小米平板6', value: 5, purchase_date: '2024-04-01', installation_date: '2024-04-05', location: '办公室' },
                    { name: '海康监控球机', value: 2, purchase_date: '2024-05-20', installation_date: '2024-05-25', location: '工厂屋顶' },
                    { name: '显示器', value: 20, purchase_date: '2024-01-15', installation_date: '2024-01-20', location: '办公室' },
                    { name: '固态硬盘', value: 25, purchase_date: '2024-01-15', installation_date: '2024-01-20', location: '办公室' },
                    { name: '会议看板', value: 1, purchase_date: '2024-01-15', installation_date: '2024-01-20', location: '会议室' },
                    { name: '对讲机', value: 22, purchase_date: '2024-01-15', installation_date: '2024-01-20', location: '车间及办公室' },
                    { name: '电脑内存条', value: 12, purchase_date: '2024-01-15', installation_date: '2024-01-20', location: '办公室' },
                    { name: '水晶头', value: 200, purchase_date: '2024-01-15', installation_date: '2024-01-20', location: '织造车间' },
                    { name: '防火墙', value: 1, purchase_date: '2024-01-15', installation_date: '2024-01-20', location: 'FCDC机房' },
                    { name: 'PD43打印机打印头', value: 10, purchase_date: '2024-01-15', installation_date: '2024-01-20', location: '办公室及车间' },
                ],
                dailyIssuesData: [
                    { name: '蓝屏死机', value: 10 },
                    { name: '打印机故障', value: 62 },
                    { name: '网络故障', value: 5 },
                    { name: '系统崩溃', value: 10 },
                    { name: '系统恢复', value: 8 },
                    { name: '硬盘故障', value: 26 },
                    { name: '软件安装', value: 25 },
                    { name: '软件升级', value: 20 },
                    { name: '病毒清除', value: 5 },
                    { name: '数据恢复', value: 3 },
                    { name: 'AGV故障', value: 5 }
                ],
                newSystemsData: [
                    { department: '织造车间', system: '织造翻改系统', value: 1 },
                    { department: '织造车间', system: '设备保养系统', value: 1 },
                    { department: '打样车间', system: '样品巡检系统', value: 1 },
                    { department: '样品测试', system: '样品测试系统', value: 1 },
                    { department: '原料仓库', system: '仓库温湿度看板', value: 1 },
                    { department: '办公室', system: 'APT防御看板', value: 1 },
                ],
                informationSecurityData: [
                    { name: '桌面管理软件安装', value: 78 },
                    { name: '瑞星杀毒安装', value: 40 },
                    { name: 'USB端口封锁', value: 80 },
                    { name: '电脑加域', value: 98 },
                    { name: '电脑漏洞修复', value: 221 },
                    { name: '紧急事件处理（电脑漏洞风险-永恒之蓝）', value: 30 },
                ],
                fullscreenLoading: true, // 加载状态
            },
            mounted() {
                window.addEventListener('resize', this.handleResize);
                window.addEventListener('scroll', this.handleScroll);
                setTimeout(() => {
                this.renderDepartmentRepairsChart();
                this.renderEquipmentRepairsChart();
                this.renderNewEquipmentChart();
                this.renderDailyIssuesChart();
                this.renderInformationSecurityChart();
                this.renderNewSystemsChart();
                    this.fullscreenLoading = false;
                }, 1000);
            },
            beforeDestroy() {
                window.removeEventListener('resize', this.handleResize);
                window.removeEventListener('scroll', this.handleScroll);
            },
            methods: {
                renderDepartmentRepairsChart() {
                    const departmentRepairsChart = echarts.init(document.getElementById('department-repairs-chart'));
                    const departmentRepairsOption = {
                        title: {
                            text: '各部门维修次数统计',
                            left: 'center'
                        },
                        color: ['#FF6347'],
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'shadow'
                            }
                        },
                        xAxis: {
                            type: 'category',
                            data: this.repairData.map(item => item.department),
                            axisLabel: {
                                rotate: -45
                            }
                        },
                        yAxis: {
                            type: 'value'
                        },
                        series: [
                            {
                                name: '总维修次数',
                                type: 'bar',
                                radius: '80%',
                                position: 'center',
                                data: this.repairData.map(item => item.total_repairs),
                                emphasis: {
                                    itemStyle: {
                                        shadowBlur: 10,
                                        shadowOffsetX: 0,
                                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                                    }
                                }
                            }
                        ]
                    };
                    departmentRepairsChart.setOption(departmentRepairsOption);
                    window.addEventListener('resize', () => { departmentRepairsChart.resize(); });
                },
                renderEquipmentRepairsChart() {
                    const equipmentRepairsChart = echarts.init(document.getElementById('equipment-repairs-chart'));
                    const equipmentRepairsOption = {
                        title: {
                            text: '各类型设备维修次数统计',
                            left: 'center'
                        },
                        color: ['#FF6347', '#50C878', '#318CE7', '#20B2AA', '#FFD700', '#FF69B4', '#8A2BE2', '#FF4500', '#2E8B57', '#4682B4', '#D2691E', '#9ACD32'],
                        tooltip: {
                            trigger: 'item',
                            formatter: '{a} <br/>{b} : {c} ({d}%)'
                        },
                        series: [
                            {
                                name: '设备维修次数',
                                type: 'pie',
                                radius: '80%',
                                center: ['50%', '60%'],
                                data: [
                                    { value: 26, name: '监控' },
                                    { value: 10, name: '电脑主机安装' },
                                    { value: 146, name: '电脑维修' },
                                    { value: 27, name: '显示器维修' },
                                    { value: 12, name: '电子称维修' },
                                    { value: 25, name: '扫描仪维修' },
                                    { value: 3, name: '投影仪' },
                                    { value: 62, name: '打印机' },
                                    { value: 8, name: '电话机' },
                                    { value: 10, name: '交换机' },
                                    { value: 13, name: '门禁控制器' },
                                    { value: 7, name: '监控电源盒' },
                                    { value: 5, name: '无线AP' },
                                    { value: 31, name: '网络布线' },
                                    { value: 2, name: 'POE电源盒' },
                                    { value: 56, name: '电脑硬盘' },
                                    { value: 52, name: '升级win10' },
                                    { value: 8, name: '升级win11' }
                                ],
                                emphasis: {
                                    itemStyle: {
                                        shadowBlur: 10,
                                        shadowOffsetX: 0,
                                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                                    }
                                }
                            }
                        ]
                    };
                    equipmentRepairsChart.setOption(equipmentRepairsOption);
                    window.addEventListener('resize', () => { equipmentRepairsChart.resize(); });
                },
                renderNewEquipmentChart() {
                    const newEquipmentChart = echarts.init(document.getElementById('new-equipment-chart'));
                    const newEquipmentOption = {
                        title: {
                            text: '2024新增IT设备及配件数量统计',
                            left: 'center'
                        },
                        color: ['#50C878'],
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'shadow'
                            }
                        },
                        xAxis: {
                            type: 'category',
                            data: this.newEquipmentData.map(item => item.name),
                             axisLabel: {
                                rotate: -45
                            }
                        },
                        yAxis: {
                            type: 'value'
                        },
                        series: [
                            {
                                name: '新增设备',
                                type: 'bar',
                                data: this.newEquipmentData.map(item => item.value),
                                emphasis: {
                                    itemStyle: {
                                        shadowBlur: 10,
                                        shadowOffsetX: 0,
                                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                                    }
                                }
                            }
                        ]
                    };
                    newEquipmentChart.setOption(newEquipmentOption);
                    window.addEventListener('resize', () => { newEquipmentChart.resize(); });
                },
                renderDailyIssuesChart() {
                    const dailyIssuesChart = echarts.init(document.getElementById('daily-issues-chart'));
                    const dailyIssuesOption = {
                        title: {
                            text: '工厂日常网络故障次数统计分析',
                            left: 'center'
                        },
                        color: ['#318CE7'],
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'shadow'
                            }
                        },
                        xAxis: {
                            type: 'category',
                            data: this.dailyIssuesData.map(item => item.name),
                            axisLabel: {
                                rotate: -45
                            }
                        },
                        yAxis: {
                            type: 'value',
                            axisLabel: {
                                formatter: '{value}'
                            }
                        },
                        series: [
                            {
                                name: '',
                                type: 'bar',
                                data: this.dailyIssuesData.map(item => item.value),
                                emphasis: {
                                    itemStyle: {
                                        shadowBlur: 10,
                                        shadowOffsetX: 0,
                                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                                    }
                                }
                            }
                        ]
                    };
                    dailyIssuesChart.setOption(dailyIssuesOption);
                    window.addEventListener('resize', () => { dailyIssuesChart.resize(); });
                },
                renderInformationSecurityChart() {
                    const informationSecurityChart = echarts.init(document.getElementById('information-security-chart'));
                    const informationSecurityOption = {
                        title: {
                            text: '工厂信息安全',
                            left: 'center'
                        },
                        color: ['#FF6347', '#50C878', '#318CE7'],
                        tooltip: {
                            trigger: 'item',
                            formatter: '{a} <br/>{b} : {c} ({d}%)'
                        },
                        series: [
                            {
                                name: '信息安全措施',
                                type: 'pie',
                                radius: '80%',
                                center: ['50%', '60%'],
                                data: this.informationSecurityData.map(item => ({
                                    name: `${item.name} (${item.value})台/次`,
                                    value: item.value
                                })),
                                emphasis: {
                                    itemStyle: {
                                        shadowBlur: 10,
                                        shadowOffsetX: 0,
                                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                                    }
                                }
                            }
                        ]
                    };
                    informationSecurityChart.setOption(informationSecurityOption);
                    window.addEventListener('resize', () => { informationSecurityChart.resize(); });
                },
                renderNewSystemsChart() {
                    const newSystemsChart = echarts.init(document.getElementById('new-systems-chart'));
                    const newSystemsOption = {
                        title: {
                            text: '2024系统开发数量统计',
                            left: 'center'
                        },
                        color: ['#318CE7'],
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'shadow'
                            }
                        },
                        xAxis: {
                            type: 'category',
                            data: this.newSystemsData.map(item => item.system),
                            axisLabel: {
                                rotate: -45
                            }
                        },
                        yAxis: {
                            type: 'value'
                        },
                        series: [
                            {
                                name: '新系统开发',
                                type: 'bar',
                                data: this.newSystemsData.map(item => item.value),
                                emphasis: {
                                    itemStyle: {
                                        shadowBlur: 10,
                                        shadowOffsetX: 0,
                                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                                    }
                                }
                            }
                        ]
                    };
                    newSystemsChart.setOption(newSystemsOption);
                    window.addEventListener('resize', () => { newSystemsChart.resize(); });
                },
                handleResize() {
                    this.renderDepartmentRepairsChart();
                    this.renderEquipmentRepairsChart();
                    this.renderNewEquipmentChart();
                    this.renderDailyIssuesChart();
                    this.renderInformationSecurityChart();
                    this.renderNewSystemsChart();
                },
                handleScroll() {
                    const scrollToTopButton = document.querySelector('.scroll-to-top');
                    if (window.scrollY > 200) {
                        scrollToTopButton.style.display = 'flex';
                    } else {
                        scrollToTopButton.style.display = 'none';
                    }
                },
                scrollToTop() {
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                }
            },
            computed: {
                totalRepairs() {
                    return this.repairData.reduce((sum, item) => sum + item.total_repairs, 0);
                },
                totalMonitor() {
                    return this.repairData.reduce((sum, item) => sum + item.monitor, 0);
                },
                totalNewComputer() {
                    return this.repairData.reduce((sum, item) => sum + item.new_computer, 0);
                },
                totalComputerRepair() {
                    return this.repairData.reduce((sum, item) => sum + item.computer_repair, 0);
                },
                totalMonitorRepair() {
                    return this.repairData.reduce((sum, item) => sum + item.monitor_repair, 0);
                },
                totalScaleRepair() {
                    return this.repairData.reduce((sum, item) => sum + item.scale_repair, 0);
                },
                totalScannerRepair() {
                    return this.repairData.reduce((sum, item) => sum + item.scanner_repair, 0);
                },
                totalProjector() {
                    return this.repairData.reduce((sum, item) => sum + item.projector, 0);
                },
                totalPrint() {
                    return this.repairData.reduce((sum, item) => sum + item.print, 0);
                },
                totalTelephone() {
                    return this.repairData.reduce((sum, item) => sum + item.telephone, 0);
                },
                totalRadio() {
                    return this.repairData.reduce((sum, item) => sum + item.radio, 0);
                },
                totalSwitch() {
                    return this.repairData.reduce((sum, item) => sum + item.switch, 0);
                },
                totalAccessControl() {
                    return this.repairData.reduce((sum, item) => sum + item.access_control, 0);
                },
                totalCameraRegion() {
                    return this.repairData.reduce((sum, item) => sum + item.camera_region, 0);
                },
                totalWifiAp() {
                    return this.repairData.reduce((sum, item) => sum + item.wifi_ap, 0);
                },
                totalWangxian() {
                    return this.repairData.reduce((sum, item) => sum + item.wangxian, 0);
                },
                totalPoe() {
                    return this.repairData.reduce((sum, item) => sum + item.poe, 0);
                },
                totalHardDrive() {
                    return this.repairData.reduce((sum, item) => sum + item.hard_drive, 0);
                },
                totalUpgradeWin10() {
                    return this.repairData.reduce((sum, item) => sum + item.upgrade_win10, 0);
                },
                totalUpgradeWin11() {
                    return this.repairData.reduce((sum, item) => sum + item.upgrade_win11, 0);
                }
            }
        });
    </script>
        <script>
        document.querySelector('.scroll-to-top').addEventListener('click', function() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
        document.getElementById('report-date').textContent = new Date().toLocaleDateString();
    </script>
    </body>
</html>