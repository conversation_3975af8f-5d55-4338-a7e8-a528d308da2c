<!-- Start of Selection -->
<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Flyknit设备管家-备件出库通知</title>
        <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            color: #fff;
        }
        .container {
            width: 70%;
            margin: 20px auto;
            background-color: #333;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            color: #fff;
        }
        .header .report-date {
            margin-top: 10px;
            font-size: 16px;
            color: #ccc;
        }
        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 2px solid #555;
            padding: 10px 0;
            font-weight: bold;
        }
        .item {
            flex: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #555;
            padding: 10px 0;
        }
        .item img {
            width: 50px;
            height: 50px;
            object-fit: cover;
            margin-right: 20px;
        }
        .item-details {
            flex: 1;
            display: flex;
            justify-content: space-around;
            align-items: flex-end;
        }
        .item-details p {
            flex: 1;
            margin: 0;
            color: #fff;
            text-align: right;
        }
        #chart, #chart2, #chart3, #chart4 {
            width: 100%;
            height: 500px;
            margin-top: 50px;
        }
    </style>
    <script src="./vue.js"></script>
    <script src="./echart.js"></script>
    </head>
    <body>
        <div id="app" class="container">
            <div class="header">
                <h1>Flyknit设备管家-备件出库通知</h1>
                <div class="report-date">{{ new Date().toLocaleString() }}</div>
            </div>
           <div style="width: 95%;margin: 0 auto; padding: 10px;">
             <div class="table-header">
                <div>配件图片</div>
                <div>配件编号</div>
                <div>配件名称</div>
                <div>领出数量</div>
            </div>
            <div class="item" v-for="part in parts" :key="part.id">
                <img :src="part.image" alt="配件图片">
                <div class="item-details">
                    <p>{{ part.code }}</p>
                    <p>{{ part.name }}</p>
                    <p>x{{ part.quantity }}</p>
                </div>
            </div>
           </div>
           <div style="width: 95%;margin: 0 auto; padding: 10px;">
             <div id="chart2"></div>
            <div id="chart4"></div>
           </div>
        </div>
        <script>
            new Vue({
                el: '#app',
                data: {
                    parts: [
                        { id: 1, code: 'F0534', name: '整个吸管', quantity: 10, image: 'http://***********99:8082/Uploads\\20230401\\1u38z2t21dvk000000003.png', department: '织造车间' },
                        { id: 2, code: 'F0267', name: '弹簧', quantity: 100, image: 'http://***********99:8082/Uploads\\20230401\\1u38z2t21dvk000000003.png', department: '打样车间' },
                        { id: 3, code: 'F0900', name: '齿形皮带', quantity: 20, image: 'http://***********99:8082/Uploads\\20230401\\1u38z2t21dvk000000003.png', department: '织造车间' },
                        { id: 4, code: 'F0739', name: '倒纱臂4+5', quantity: 200, image: 'http://***********99:8082/Uploads\\20230401\\1u38z2t21dvk000000003.png', department: '织造车间' },
                        { id: 5, code: 'F0151', name: '编织定位三角', quantity: 48, image: 'http://***********99:8082/Uploads\\20230401\\1u38z2t21dvk000000003.png', department: '织造车间' },
                        { id: 6, code: 'F0170', name: '带帽螺丝', quantity: 202, image: 'http://***********99:8082/Uploads\\20230401\\1u38z2t21dvk000000003.png', department: '打样车间' },
                        { id: 7, code: 'F0393', name: 'LGL轴沙器板子', quantity: 10, image: 'http://***********99:8082/Uploads\\20230401\\1u38z2t21dvk000000003.png', department: '织造车间' },
                    ],
                    users: [
                        { name: '胡友利', total: 280, department: '织造车间' },
                        { name: '王灿', total: 300, department: '打样车间' },
                        { name: '张守苒', total: 250, department: '织造车间' },
                        { name: '查张兵', total: 220, department: '打样车间' },
                        { name: '潘国继', total: 200, department: '织造车间' },
                        { name: '李正涛', total: 180, department: '打样车间' },
                        { name: '王佳俊', total: 160, department: '织造车间' },
                        { name: '刘腾', total: 140, department: '打样车间' }
                    ]
                },
                mounted() {
                    this.renderChartByDepartment();
                    this.renderUserChartByDepartment();
                    window.addEventListener('resize', this.handleResize);
                },
                beforeDestroy() {
                    window.removeEventListener('resize', this.handleResize);
                },
                methods: {
                    renderChartByDepartment() {
                        const chart2 = echarts.init(document.getElementById('chart2'));
                        const weavingParts = this.parts.filter(part => part.department === '织造车间');
                        const samplingParts = this.parts.filter(part => part.department === '打样车间');
                        const option2 = {
                            title: {
                                text: '配件（月）领出数量统计（Top 20）',
                                textStyle: { color: '#fff' }
                            },
                            color: ['#007bff', '#ff6b6b'],
                            tooltip: {
                                trigger: 'axis',
                                axisPointer: {
                                    type: 'shadow'
                                }
                            },
                            legend: {
                                data: ['织造车间', '打样车间'],
                                textStyle: { color: '#fff' }
                            },
                            xAxis: {
                                type: 'category',
                                data: ['整个吸管', '弹簧', '齿形皮带', '倒纱臂4+5', '编织定位三角', '带帽螺丝', 'LGL轴沙器板子'],
                                axisLabel: { color: '#fff' },
                                axisLine: {
                                    show: false,
                                    lineStyle: {
                                        color: '#fff'
                                    }
                                },
                                axisTick: { show: false }
                            },
                            yAxis: {
                                type: 'value',
                                axisLabel: { color: '#fff' },
                                axisLine: {
                                    show: false,
                                    lineStyle: {
                                        color: '#fff'
                                    }
                                },
                                axisTick: { show: false },
                                splitLine: { show: false }
                            },
                            series: [
                                {
                                    name: '织造车间',
                                    data: [10, 32, 20, 130, 48, 340, 10, 0, 100, 0, 12],
                                    type: 'bar',
                                    label: {
                                        show: true,
                                        position: 'top',
                                        color: '#fff'
                                    }
                                },
                                {
                                    name: '打样车间',
                                    data: [4, 100, 10, 45, 22, 123, 5, 0, 0, 0, 12],
                                    type: 'bar',
                                    label: {
                                        show: true,
                                        position: 'top',
                                        color: '#fff'
                                    }
                                }
                            ]
                        };
                        chart2.setOption(option2);
                        this.chart2 = chart2;
                    },
                    renderUserChartByDepartment() {
                        const chart4 = echarts.init(document.getElementById('chart4'));
                        const weavingUsers = this.users.filter(user => user.department === '织造车间');
                        const samplingUsers = this.users.filter(user => user.department === '打样车间');
                        const option4 = {
                            title: {
                                text: '各组长（月）领用数量统计',
                                textStyle: { color: '#fff' }
                            },
                            color: ['#007bff', '#ff6b6b'],
                            tooltip: {
                                trigger: 'item'
                            },
                            grid: {
                                top: '10%',
                                left: '3%',
                                right: '4%',
                                bottom: '3%',
                                containLabel: true
                            },
                            legend: {
                                data: ['织造车间', '打样车间'],
                                textStyle: { color: '#fff' }
                            },
                            series: [
                                {
                                    name: '织造车间',
                                    type: 'pie',
                                    data: weavingUsers.map(user => ({ value: user.total, name: user.name })),
                                    roseType: 'radius',
                                    label: {
                                        show: true,
                                        formatter: '{b}: {c} ({d}%)',
                                        color: '#fff'
                                    }
                                },
                                {
                                    name: '打样车间',
                                    type: 'pie',
                                    radius: '50%',
                                    data: samplingUsers.map(user => ({ value: user.total, name: user.name })),
                                    roseType: 'radius',
                                    label: {
                                        show: true,
                                        formatter: '{b}: {c} ({d}%)',
                                        color: '#fff'
                                    }
                                }
                            ]
                        };
                        chart4.setOption(option4);
                        this.chart4 = chart4;
                    },
                    handleResize() {
                        if (this.chart2) {
                            this.chart2.resize();
                        }
                        if (this.chart4) {
                            this.chart4.resize();
                        }
                    }
                }
            });
        </script>
    </body>
</html>
<!-- End of Selection -->