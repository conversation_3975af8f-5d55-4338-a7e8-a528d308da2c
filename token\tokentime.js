/*
 * @Author: flyknit <EMAIL>
 * @Date: 2023-05-26 10:37:13
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-07-19 09:08:29
 * @FilePath: \electronic-filef:\maintainceMe\token\tokentime.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const crypto = require('crypto'); // 加密模块
// 导出配置文件
const secretKey = 'R3ZD8J1G99N9YCB0L18OH785LRO59P8NEPCV' // 32位随机字符串
const security = {
	secretKey, // 32位随机字符串
	expiresIn: '12h'// 12h过期
};
module.exports = security;