const crypto = require('crypto'); // 引入crypto模块
// 密码MD5加密
const encryptPassword = function(password) {
    const salt = 'randomSalt'; // 设置盐值，可以是随机字符串或者用户信息中的某个字段
    const hash = crypto.createHmac('sha256', salt); // 创建加密对象，使用sha256算法和盐值
    hash.update(password); // 加密密码
    const encryptedPassword = hash.digest('hex'); // 获取加密后的密码，使用hex编码
    return encryptedPassword;
}
  
const passwordHash = function(password) {
    const hash = crypto.createHmac('sha256'); // 创建加密对象，使用sha256算法和盐值
    hash.update(password); // 加密密码
    const encryptedPassword = hash.digest('hex'); // 获取加密后的密码，使用hex编码
    return encryptedPassword;
}

module.exports = { encryptPassword, passwordHash };