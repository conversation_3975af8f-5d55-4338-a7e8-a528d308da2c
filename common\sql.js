/*
 * @Author: flyknit <EMAIL>
 * @Date: 2023-11-09 15:58:11
 * @LastEditors: flyknit <EMAIL>
 * @LastEditTime: 2024-06-04 13:34:02
 * @FilePath: \electronic-filed:\gitee\nike-backend\config\sqlsrv.js
 * @Description: sql server
 * Copyright (c) 2024 by ${<EMAIL>}, All Rights Reserved.
 */
// 引入mssql
const mssql = require("mssql");

// 数据库配置文件
const configs = {
  vietnam: {
    user: "useryxw",
    password: "yxw.123",
    server: "***********",
    database: "kywlck_hy",
    options: {
      encrypt: false, // Use this if you're on Windows Azure
      enableArithAbort: true,
      connectionTimeout: 15000,
    },
  },
  ningbo: {
    user: "sch_yy_ky",
    password: "szky.20230610",
    server: "************",
    database: "kywlck_hy",
    options: {
      encrypt: false,
      enableArithAbort: true,
      connectionTimeout: 15000,
    },
  },
  hysy: {
    user: "schhysy",
    password: "Schy.20240527",
    server: "***********",
    database: "hysygl",
    options: {
      encrypt: false,
      enableArithAbort: true,
      connectionTimeout: 15000,
    },
  },
};

// 创建数据库连接池管理器
const createPool = (config) => {
  const pool = new mssql.ConnectionPool(config);
  const poolConnect = pool.connect();

  poolConnect
    .then(() => console.log(`${config.database} 连接成功`))
    .catch((err) => console.error(`${config.database} 连接失败`, err));

  return {
    pool,
    poolConnect,
  };
};

// 创建连接池实例
const vietnamPool = createPool(configs.vietnam);
const ningboPool = createPool(configs.ningbo);
const hysyPool = createPool(configs.hysy);

// 通用的查询函数
const queryData = (pool, sql, params) => {
  return new Promise((resolve, reject) => {
    pool.poolConnect
      .then(() => {
        const request = new mssql.Request(pool.pool);
        request.input("input_parameter", mssql.Int, params);
        request.query(sql, (err, result) => {
          if (err) {
            reject(err);
          } else {
            let data = [];
            result.recordsets.forEach((item) => {
              data = [...data, ...item];
            });
            resolve(data);
          }
        });
      })
      .catch((err) => {
        reject(err);
      });
  });
};

// 导出函数
module.exports = {
  fetchFlynit: (sql, params) => queryData(vietnamPool, sql, params), // 越南华耀生产管理
  fetchNingbo: (sql, params) => queryData(ningboPool, sql, params), // 宁波华耀生产管理
  fetchSampleFcdc: (sql, params) => queryData(hysyPool, sql, params), // 越南打样试样管理
};
