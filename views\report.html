<html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <script
            src="https://cdn.tailwindcss.com?plugins=forms,typography"></script>
        <script
            src="https://unpkg.com/unlazy@0.11.3/dist/unlazy.with-hashing.iife.js"
            defer init></script>
        <script type="text/javascript">
			window.tailwind.config = {
				darkMode: ['class'],
				theme: {
					extend: {
						colors: {
							border: 'hsl(var(--border))',
							input: 'hsl(var(--input))',
							ring: 'hsl(var(--ring))',
							background: 'hsl(var(--background))',
							foreground: 'hsl(var(--foreground))',
							primary: {
								DEFAULT: 'hsl(var(--primary))',
								foreground: 'hsl(var(--primary-foreground))'
							},
							secondary: {
								DEFAULT: 'hsl(var(--secondary))',
								foreground: 'hsl(var(--secondary-foreground))'
							},
							destructive: {
								DEFAULT: 'hsl(var(--destructive))',
								foreground: 'hsl(var(--destructive-foreground))'
							},
							muted: {
								DEFAULT: 'hsl(var(--muted))',
								foreground: 'hsl(var(--muted-foreground))'
							},
							accent: {
								DEFAULT: 'hsl(var(--accent))',
								foreground: 'hsl(var(--accent-foreground))'
							},
							popover: {
								DEFAULT: 'hsl(var(--popover))',
								foreground: 'hsl(var(--popover-foreground))'
							},
							card: {
								DEFAULT: 'hsl(var(--card))',
								foreground: 'hsl(var(--card-foreground))'
							},
						},
					}
				}
			}
		</script>
        <style type="text/tailwindcss">
			@layer base {
				:root {
					--background: 0 0% 100%;
--foreground: 240 10% 3.9%;
--card: 0 0% 100%;
--card-foreground: 240 10% 3.9%;
--popover: 0 0% 100%;
--popover-foreground: 240 10% 3.9%;
--primary: 240 5.9% 10%;
--primary-foreground: 0 0% 98%;
--secondary: 240 4.8% 95.9%;
--secondary-foreground: 240 5.9% 10%;
--muted: 240 4.8% 95.9%;
--muted-foreground: 240 3.8% 46.1%;
--accent: 240 4.8% 95.9%;
--accent-foreground: 240 5.9% 10%;
--destructive: 0 84.2% 60.2%;
--destructive-foreground: 0 0% 98%;
--border: 240 5.9% 90%;
--input: 240 5.9% 90%;
--ring: 240 5.9% 10%;
--radius: 0.5rem;
				}
				.dark {
					--background: 240 10% 3.9%;
--foreground: 0 0% 98%;
--card: 240 10% 3.9%;
--card-foreground: 0 0% 98%;
--popover: 240 10% 3.9%;
--popover-foreground: 0 0% 98%;
--primary: 0 0% 98%;
--primary-foreground: 240 5.9% 10%;
--secondary: 240 3.7% 15.9%;
--secondary-foreground: 0 0% 98%;
--muted: 240 3.7% 15.9%;
--muted-foreground: 240 5% 64.9%;
--accent: 240 3.7% 15.9%;
--accent-foreground: 0 0% 98%;
--destructive: 0 62.8% 30.6%;
--destructive-foreground: 0 0% 98%;
--border: 240 3.7% 15.9%;
--input: 240 3.7% 15.9%;
--ring: 240 4.9% 83.9%;
				}
			}
		</style>
    </head>
    <body>

        <div class="container mx-auto p-6 bg-background text-foreground">
            <h1 class="text-2xl font-bold mb-4 text-center">机修年度工作汇总</h1>

            <h2 class="text-xl font-semibold mb-2 pt-4 pb-2">01 年度维修机台总数排名</h2>
            <p class="text-center">年度总维修次数: <span id="total-repairs"></span></p>
            <!-- 柱状图 -->
            <div class="grid grid-cols-1 gap-2">
                <div class="p-4">
                    <div class="relative" style="height: 400px;">
                        <div id="repair-rank" style="height: 100%;"></div>
                    </div>
                </div>
            </div>
            <!-- 折线图 -->
            <div class="grid grid-cols-1 gap-2">
                <div class="p-4">
                    <div class="relative" style="height: 400px;">
                        <div id="monthly-repair-trend" style="height: 100%;"></div>
                    </div>
                </div>
            </div>

            <h2 class="text-xl font-semibold mb-2 pt-4 pb-2">02 年度翻改机台数量排名</h2>
            <div class="grid grid-cols-1 gap-10">
                <div class="p-4" style="height: 400px;">
                    <div class="relative" style="height: 100%;">
                        <div id="rebuild-rank" style="height: 100%;"></div>
                    </div>
                </div>
            </div>

            <h2 class="text-xl font-semibold mb-2">03 年度机台调机排名</h2>
            <div class="grid grid-cols-2 gap-4">
                <div class="p-4" style="height: 200px;">
                    柱状图
                </div>
            </div>

            <h2 class="text-xl font-semibold mb-2">04 关键指标评估</h2>
            <div class="grid grid-cols-2 gap-4">
                <div class="p-4" style="height: 200px;">
                    柱状图
                </div>
                <div class="p-4" style="height: 200px;">
                    柱状图
                </div>
            </div>

            <h2 class="text-xl font-semibold mb-2">05 历史趋势</h2>
            <div class="p-4">
                <p>体重变化趋势 (kg)</p>
                <div id="weight-trend" style="height: 200px;"></div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/vue@2"></script>
        <script
            src="https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js"></script>
        <script>
        var repairChart = echarts.init(document.getElementById('repair-rank'));
        var repairData = [16194, 15620, 13898, 13005, 12675, 12272, 12061, 11806, 10400, 10024, 7692, 6096, 5959, 5835, 5347, 5197, 4853, 4850, 4231, 4102, 1451, 502, 185, 91, 73, 8, 0];
        var totalRepairs = repairData.reduce((a, b) => a + b, 0);
        document.getElementById('total-repairs').innerText = totalRepairs;

        var repairOption = {
            title: {
                text: '维修次数',
                textStyle: {
                    color: '#ffffff'
                }
            },
            color: ['#FB9755'],
            tooltip: {},
            xAxis: {
                type: 'category',
                data: ['伍祚国', '陈文强', '张忠情', '孙春光', '王志春', '阮玉清', '依旁露', '黎明贤', '周部叉', '范青太和', '依初鸾', '武明要', '聂明伟', '周素鸥', '阮明王', '阮奋斗', '依何补', '刘爱方', '徐伟', '张友成', '胡福寿', '林国总', '张丹', '黄少锋', '潘软登', '胡友利', '黎明新'],
                axisLabel: {
                    color: '#ffffff',
                    interval: 0,
                    rotate: 40
                },
                axisLine: {
                    show: false
                }
            },
            yAxis: {
                type: 'value',
                axisLabel: {
                    color: '#ffffff'
                },
                axisLine: {
                    show: false
                }
            },
            series: [{
                name: '维修机台总数',
                type: 'bar',
                data: repairData,
                itemStyle: {
                    color: '#FB9755'
                },
                label: {
                    show: true,
                    position: 'top',
                    color: '#ffffff',
                    formatter: function(params) {
                        return params.value;
                    }
                }
            }]
        };
        repairChart.setOption(repairOption);

        var monthlyRepairChart = echarts.init(document.getElementById('monthly-repair-trend'));
        var monthlyRepairOption = {
            title: {
                text: '月维修次数',
                textStyle: {
                    color: '#ffffff'
                }
            },
            color: ['#FB9755'],
            tooltip: {},
            xAxis: {
                type: 'category',
                data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                axisLabel: {
                    color: '#ffffff'
                },
                axisLine: {
                    show: false
                }
            },
            yAxis: {
                type: 'value',
                axisLabel: {
                    color: '#ffffff'
                },
                axisLine: {
                    show: false
                }
            },
            series: [{
                name: '月维修次数',
                type: 'line',
                data: [1200, 1100, 1300, 1400, 1500, 1600, 1700, 1800, 1900, 2000, 2100, 2200],
                itemStyle: {
                    color: '#FB9755'
                },
                label: {
                    show: true,
                    position: 'top',
                    color: '#ffffff',
                    formatter: function(params) {
                        return params.value;
                    }
                }
            }]
        };
        monthlyRepairChart.setOption(monthlyRepairOption);

        var rebuildChart = echarts.init(document.getElementById('rebuild-rank'));
        var rebuildOption = {
            title: {
                text: '翻改次数',
                textStyle: {
                    color: '#ffffff'
                }
            },
            color: ['#000000'],
            tooltip: {},
            xAxis: {
                type: 'category',
                data: ['伍祚国', '陈文强', '张忠情', '孙春光', '王志春', '阮玉清', '依旁露', '黎明贤', '周部叉', '范青太和', '依初鸾', '武明要', '聂明伟', '周素鸥', '阮明王', '阮奋斗', '依何补', '刘爱方', '徐伟', '张友成', '胡福寿', '林国总', '张丹', '黄少锋', '潘软登', '胡友利', '黎明新'],
                axisLabel: {
                    color: '#ffffff',
                    interval: 0,
                    rotate: 40
                },
                axisLine: {
                    show: false
                }
            },
            yAxis: {
                type: 'value',
                axisLabel: {
                    color: '#ffffff'
                },
                axisLine: {
                    show: false
                }
            },
            series: [{
                name: '翻改机台数量',
                type: 'bar',
                data: [12000, 11500, 11000, 10500, 10000, 9500, 9000, 8500, 8000, 7500, 7000, 6500, 6000, 5500, 5000, 4500, 4000, 3500, 3000, 2500, 2000, 1500, 1000, 500, 250, 100, 50],
                itemStyle: {
                    color: '#20B2AA'
                },
                label: {
                    show: true,
                    position: 'top',
                    color: '#ffffff',
                    formatter: function(params) {
                        return params.value;
                    }
                }
            }]
        };
        rebuildChart.setOption(rebuildOption);

        var myChart = echarts.init(document.getElementById('weight-trend'));
        var option = {
            title: {
                text: '体重变化趋势'
            },
            tooltip: {},
            xAxis: {
                data: ['2024-06', '2024-07', '2024-08', '2024-09', '2024-10']
            },
            yAxis: {},
            series: [{
                name: '体重',
                type: 'line',
                data: [72.2, 70.6, 70.8, 70.3, 69.6]
            }]
        };
        myChart.setOption(option);
</script>
<style>
  :root {
      --background: #333;
      --foreground: #ffffff;
      --border: #e0e0e0;
  }
  body {
      background-color: var(--background);
      color: var(--foreground);
  }
</style>
</body>
</html>