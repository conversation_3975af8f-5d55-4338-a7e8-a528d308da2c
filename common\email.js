// Description: 邮件发送
const nodemailer = require("nodemailer");
const { query } = require("./connection");
// 导入配置文件
const config = require("../config/config");
const baseUrl = config.baseUrl;
const path = require("path");
const fs = require("fs");
const puppeteer = require("puppeteer");

async function queryEmail() {
  return new Promise((resolve, reject) => {
    query("select * from email_setting", (err, data) => {
      if (err) {
        reject(err);
      } else {
        resolve(data[0]);
      }
    });
  });
}

async function getEmail(mail, msg) {
  try {
    const email = await queryEmail();
    const transporter = nodemailer.createTransport({
      host: email.host,
      port: email.port,
      secure: false,
      secureProtocol: "TLSv1_2_method", // 指定正确的SSL版本
      auth: {
        user: email.email,
        pass: email.password,
      },
    });
    const screenshotPath = await takeScreenshot(msg); // Pass the message to takeScreenshot
    const result = await sendMail(transporter, mail, msg, screenshotPath); // Pass the screenshot path to sendMail
    return result;
  } catch (error) {
    console.error(error);
  }
}

async function takeScreenshot(msg) {
  const browser = await puppeteer.launch();
  const page = await browser.newPage();
  // set viewport width and height
  await page.setViewport({ width: 1920, height: 1080 });

  // Load the HTML from a URL
  const url = "http://127.0.0.1:8081/"; // Replace with the actual URL
  await page.goto(url, { waitUntil: "networkidle0" });

  // Add the message to the page
  await page.evaluate((msg) => {
    const body = document.querySelector("body");
    const messageElement = document.createElement("p");
    messageElement.textContent = msg;
    body.appendChild(messageElement);
  }, msg);

  // Save the screenshot as a file in the root directory's screenshorts folder
  const screenshotPath = path.join(process.cwd(), "screenshorts/screenshot.png");
  await page.screenshot({ path: screenshotPath, fullPage: true });
  await browser.close();

  return screenshotPath;
}

function sendMail(transporter, mail, msg, screenshotPath) {
  return new Promise((resolve, reject) => {
    const date = new Date();
    let year = date.getFullYear();
    let month = date.getMonth() + 1;
    let day = date.getDate();
    if (month < 10) {
      month = `0${month}`;
    }
    if (day < 10) {
      day = `0${day}`;
    }

    const mailOptions = {
      from: transporter.options.auth.user,
      to: mail,
      subject: "Flyknit设备管家-备件出库通知",
      html: `<img src="cid:screenshot" alt="备件出库通知">`,
      attachments: [
        {
          filename: "screenshot.png",
          path: screenshotPath,
          cid: "screenshot", // same cid value as in the html img src
        },
      ],
    };

    transporter.sendMail(mailOptions, (error, info) => {
      if (error) {
        reject(error);
      } else {
        resolve(info.messageId);
      }
    });
  });
}

module.exports = {
  getEmail,
};
