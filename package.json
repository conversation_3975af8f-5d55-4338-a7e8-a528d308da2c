{"name": "maintainceme", "version": "1.0.0", "description": "backend of maintaince", "main": "nodemon app.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon app.js"}, "author": "ya<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"bcrypt": "^5.1.0", "connect-redis": "^7.1.0", "cors": "^2.8.5", "exceljs": "^4.3.0", "express": "^4.18.2", "express-session": "^1.17.3", "http-status-codes": "^2.2.0", "ioredis": "^5.6.1", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "logger": "^0.0.1", "moment": "^2.29.4", "morgan": "^1.10.0", "mssql": "^11.0.1", "multer": "^1.4.5-lts.1", "mysql2": "^3.3.2", "node-xlsx": "^0.23.0", "nodemailer": "^6.9.4", "nodemon": "^2.0.22", "puppeteer": "^23.11.1", "qrcode": "^1.5.3", "redis": "^4.6.7", "router": "^1.3.8", "useragent": "^2.3.0", "uuid": "^9.0.0", "winston": "^3.17.0", "xlsx": "^0.18.5"}}