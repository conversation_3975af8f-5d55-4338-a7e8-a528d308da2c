/*
 * @Author: flyknit <EMAIL>
 * @Date: 2023-05-24 08:36:35
 * @LastEditors: flyknit <EMAIL>
 * @LastEditTime: 2023-06-27 17:12:45
 * @FilePath: \electronic-filef:\maintainceMe\common\connection.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// 数据库信息
const mysql = require('mysql2');
const pool = mysql.createPool({
    host: '*************', // 连接的服务器(代码托管到线上后，需改为内网IP，而非外网)
    port: 3306, // mysql服务运行的端口
    database: 'maintaince', // 选择的库
    user: 'maintaince', // 用户名
    password: 'G7mLDRyekfpiri6B' // 用户密码   
})

const pool1 = mysql.createPool({
  host: '*************',
  port: 3306,
  database: 'warehouseone',
  user: 'warehouseone',
  password: '8rr5d2wTha47JrbM'
})

const searchQuery = function(sql, params) {
  return new Promise((resolve, reject) => {
    pool1.getConnection((err, connection) => {
      if (err) {
        reject(err);
      } else {
        connection.query(sql, params, (error, results) => {
          connection.release();
          if (error) {
            reject(error);
          } else {
            resolve(results);
          }
        });
      }
    });
  });
}
/**
 * 执行SQL查询
 * @param {string} sql - SQL语句
 * @param {any[]} params - 参数数组
 * @returns {Promise<any>} - 查询结果的Promise对象
 */
const query =  function(sql, params) {
    // 返回一个Promise对象
    return new Promise((resolve, reject) => {
      // 从连接池中获取一个连接
      pool.getConnection((err, connection) => {
        if (err) {
          // 连接出错，返回错误信息
          reject(err);
        } else {
          // 执行SQL语句
          connection.query(sql, params, (error, results) => {
            // 释放连接
            connection.release();
            if (error) {
              // 查询出错，返回错误信息
              reject(error);
            } else {
              // 查询成功，返回结果
              resolve(results);
            }
          });
        }
      });
    });
  }
// const TripQuery = function(sql, params, callback) {
//   // 如果没有传入params，就将第二个参数设为callback，params设为null
//   if (typeof params === 'function') {
//     callback = params;
//     params = null;
//   }

//   // 从连接池中获取一个连接
//   pool.getConnection((err, connection) => {
//     if (err) {
//       // 连接出错，返回错误信息
//       if (callback) {
//         callback(err);
//       } else {
//         return Promise.reject(err);
//       }
//     } else {
//       // 执行SQL语句
//       connection.query(sql, params, (error, results) => {
//         // 释放连接
//         connection.release();
//         if (error) {
//           // 查询出错，返回错误信息
//           if (callback) {
//             callback(error);
//           } else {
//             return Promise.reject(error);
//           }
//         } else {
//           // 查询成功，返回结果
//           if (callback) {
//             callback(null, results);
//           } else {
//             return Promise.resolve(results);
//           }
//         }
//       });
//     }
//   });
// }
const TripQuery = function(sql, params) {
  return new Promise((resolve, reject) => {
    if (typeof params === 'function') {
      params = null;
    }

    pool.getConnection((err, connection) => {
      if (err) {
        reject(err);
      } else {
        connection.query(sql, params, (error, results) => {
          connection.release();
          if (error) {
            reject(error);
          } else {
            resolve(results);
          }
        });
      }
    });
  });
}


module.exports = {
    query,
    TripQuery,
    searchQuery
}






