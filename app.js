/*
 * @Author: flyknit <EMAIL>
 * @Date: 2023-05-24 08:20:31
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-08-18 09:17:07
 * @FilePath: \electronic-filef:\maintainceMe\app.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// 引入express框架
const express = require('express');
const session = require('express-session');
// 引入全局错误处理中间件
const { errorHandler } = require('./middleware/errorHandler');
// 引入跨域模块
const cors = require('cors');
const bodyParser = require('body-parser');
const fs = require('fs');
const path = require('path');
// 引入security
const security = require('./token/tokentime');
const winston = require('winston');
const morgan = require('morgan'); // 引入morgan用于日志记录
const httpPort = 8081; // 指定服务器监听的端口号

// 创建express实例
const app = express();

// winston日志
const logger = winston.createLogger({
  level: "info",
  format: winston.format.json(),
  defaultMeta: { service: "user-service" },
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: "error.log", level: "error" }),
    new winston.transports.File({ filename: "inspection.log" }),
    new winston.transports.File({ filename: "request.log" }),
  ],
});

// 将logger挂载到全局
global.logger = logger;

// 跨域设置
app.use(cors({
  origin: ["http://10.10.131.188:8081", "http://10.10.131.188:8085", "http://10.10.131.188:8080", "http://10.10.131.29:8081", "http://10.10.131.132:8081","http://10.10.131.188:8082"],
  methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
  credentials: true,
  optionsSuccessStatus: 200
}));

// session配置
app.use(session({
  secret: security.secretKey, // 对session id 相关的cookie 进行签名
  resave: false,
  saveUninitialized: false, // 是否保存未初始化的会话
  // store: new RedisStore({ client: redisClient }),
  cookie: {
    maxAge: 8640000, // 时长
    secure: false, // 是否只用于https
    sameSite: "production" ? "none" : "lax",
  },
}));

// 输出日志到目录
const accessLogStream = fs.createWriteStream(path.join(__dirname, '/log/request.log'), { flags: 'a', encoding: 'utf8' });
app.use(morgan('combined', { stream: accessLogStream })); // 使用morgan记录请求日志
// 静态资源访问
app.use('/uploads', express.static('public'));
app.use(express.static(path.join(__dirname, "./views")));
// 全局统一返回函数
app.use((req, res, next) => {
  res.Error = (error, status = 10003, data = []) => {
    res.send({
      status,
      message: error instanceof Error ? error.message : error,
      data,
    });
  };
  res.Success = (message = "success", data = []) => {
    res.send({
      status: 200,
      message,
      data: data ? data : [],
    });
  };
  next();
});

// 解析请求体表单数据
app.use(bodyParser.urlencoded({ extended: false }));
app.use(bodyParser.json());
app.use(express.json());

// 引入自定义路由
const apirouter = require('./router/index');
app.use('', apirouter)
app.use('/api', apirouter); // 使用自定义路由
app.set('trust proxy', 1)
app.use(apirouter); // 使用自定义路由
app.use(errorHandler); // 使用全局错误处理中间件

// 启动服务
app.listen(httpPort, () => globalThis.console.log(`Server is running on port ${httpPort}`))
