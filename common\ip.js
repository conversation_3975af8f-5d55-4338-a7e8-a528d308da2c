/*
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2023-08-09 14:46:01
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-08-09 14:47:25
 * @FilePath: \electronic-filee:\编程项目\maintainceMe\common\ip.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const os = require("os");
const useragent = require("useragent");

const ip = {
    getClientIp(req) {
        let ip = req.headers["x-forwarded-for"] ||

            req.connection.remoteAddress || req.socket.remoteAddress || req.connection.socket.remoteAddress;
        if (ip.split(",").length > 0) {
            ip = ip.split(",")[0]
        }
        return ip;
    },
    getServerIp() {
        let interfaces = os.networkInterfaces();
        let IPv4 = ""
        for (let key in interfaces) {
            interfaces[key].forEach(function(details, alias) {
                if (details.family == "IPv4" && key == "en0") {
                    IPv4 = details.address;
                }
            });
        }
        return IPv4;
    },
    getAgent(req) {
        let agent = useragent.parse(req.headers["user-agent"]);
        return agent;
    }
};

module.exports = ip;
