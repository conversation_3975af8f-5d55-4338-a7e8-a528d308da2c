const redis = require('redis')
const client = redis.createClient({
    socket: {
        host: '*************',
        port: 6379
    },
    password: '12345678',
});

client.on('error', (err) => {
    console.log('Error ' + err);
});

client.on('reconnecting', function () {
    console.log('redis reconnecting')
})

client.on('end', function () {
    console.log('Redis Closed!')
})

client.on('warning', function () {
    console.log('Redis client: warning')
})

client.connect().then((err) => {
    console.log("redis服务器连接成功")
})

// 导出 redis 客户端
module.exports = client;