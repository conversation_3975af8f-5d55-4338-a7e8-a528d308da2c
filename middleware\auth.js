/*
 * @Author: flyknit <EMAIL>
 * @Date: 2023-05-24 09:48:10
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-07-27 11:32:10
 * @FilePath: \electronic-filef:\maintainceMe\middleware\auth.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// 引入jwt
const jwt = require('jsonwebtoken');
// 引入security
const security = require('../token/tokentime');
// 引入session
const session = require('express-session');
// 引入redis
const client = require('../common/redis')

// Description: 验证token中间件
const verifyToken = async (req, res, next) => {
    const authToken = req.headers.authorization?.replace('Bearer ', '');
    if (!authToken) {
      return res.status(401).send({ status: 401, message: '非法请求,请登录', data: null })
    } else {
      try {
        const secretKey = security.secretKey;
        const decoded = await jwt.verify(authToken, secretKey);
        const uid = decoded.user.userId;
        // 验证成功，将解码后的数据附加到请求对象上
        if (decoded.user && decoded.user.userId) {
          req.user = decoded.user;
        }
        // const redisToken = await client.get(`userToken:${uid}`);
        // if (!redisToken || redisToken !== authToken) {
        //   // 当前请求的 token 与 redis 中的 token 不一致，拒绝访问
        //   return res.status(401).send({ status: 401, message: 'Token令牌失效,请重新登录', data: null })
        // }
         // 当前请求的 token 与 redis 中的 token 一致，可以继续访问
        return next();
      } catch (error) {
        if (error instanceof jwt.TokenExpiredError) {
          return res.status(401).send({ status: 401, message: '身份验证失败 ,请重新登录', data: null })
        }else if(error instanceof jwt.JsonWebTokenError) {
          return res.status(401).send({ status: 401, message: '身份验证失败 ,请重新登录', data: null })
        }else {
          return res.status(401).send({ status: 401, message: '非法请求,请登录', data: null })
        }
      }
    }
  };

  module.exports = verifyToken;