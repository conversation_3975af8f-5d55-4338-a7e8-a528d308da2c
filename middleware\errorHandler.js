/*
 * @Author: flyknit <EMAIL>
 * @Date: 2023-05-15 08:16:22
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2023-08-09 08:13:28
 * @FilePath: \electronic-filee:\backend\src\middleware\errorHandler.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const winston = require('winston'); // 导入日志处理模块
const morgan = require('morgan'); // 导入日志中间件
const path = require('path');
const {logger} = require('../common/logger');
const domain = require('domain');

const errorHandler = function (err, req, res, next) {
  // res.status(500).send({status:500, message: '服务器内部错误', error: err.message}); // 返回错误状态码和提示信息
  logger.error(err.message);
  const reqDomain = domain.create();
  reqDomain.on('error', function (err) {
    res.status(err.status || 500);
    res.render('error');
  });
  reqDomain.run(next);
}

module.exports = {
  errorHandler
}
