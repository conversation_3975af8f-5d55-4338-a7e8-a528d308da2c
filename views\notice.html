<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>库存预警</title>
        <link rel="stylesheet"
            href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
        <script src="https://cdn.jsdelivr.net/npm/vue@2"></script>
        <script src="https://unpkg.com/element-ui/lib/index.js"></script>
        <script
            src="https://cdn.jsdelivr.net/npm/echarts/dist/echarts.min.js"></script>
        <style>
            body {
                background-color: #333;
            }
        .header {
            background-color: #FB6C1E;
            color: white;
            padding: 10px;
            text-align: center;
        }
        .header h1 {
            font-size: 2rem;
            font-weight: bold;
        }
        .report-page {
            font-family: Arial, sans-serif;
            width: 100%;
            box-sizing: border-box;
            overflow: hidden;
            background-color: #fff;
            padding: 1rem;
        }
        .report-header {
            background-color: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .summary-section {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            margin-bottom: 20px;
            box-sizing: border-box;
            width: 100%;
            height: 100%;
        }
        .summary-box {
            flex: 1;
            padding: 20px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            text-align: center;
            margin: 10px;
        }
        .charts-section {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
        }
        .bar-chart, .pie-chart {
            width: 100%;
            margin: 10px;
            min-width: 300px;
        }
        h3 {
            font-size: 1.2rem;
            font-weight: bold;
            letter-spacing: 0.1rem;
            color: #333;
            font-family: 'Arial', sans-serif;
            font-style: italic;
        }
        @media (max-width: 768px) {
            .summary-section, .charts-section {
                flex-direction: column;
            }
            .summary-box, .bar-chart, .pie-chart {
                margin: 10px 0;
            }
        }
        #app {
            width: 60%;
            height: 100%;
            margin: 0 auto;
            padding: 0;
        }
    </style>
    </head>
    <body>
        <div id="app">
            <div class="header">
                <h1>配件库存预警通知</h1>
            </div>
            <div class="report-page">
                <header class="report-header">
                    <h2>配件库存预警报告</h2>
                    <p>预警时间：2025-01-03 10:00:31 &nbsp;&nbsp; 配件预警数：6</p>
                </header>

                <section class="summary-section">
                    <el-divider content-position="center">配件预警表</el-divider>
                    <div class="summary-box">
                        <el-table :data="warningItems" style="width: 100%"
                            size="medium" stripe>
                            <el-table-column prop="name" label="配件名称"
                                align="center"></el-table-column>
                            <el-table-column prop="id" label="配件编号"
                                align="center"></el-table-column>
                            <el-table-column prop="loss" label="损耗数量"
                                align="center"></el-table-column>
                            <el-table-column prop="currentStock" label="剩余库存"
                                align="center"></el-table-column>
                            <el-table-column prop="currentStock" label="预警库存"
                                align="center"></el-table-column>
                            <el-table-column prop="warningStatus" label="预警状态"
                                align="center">
                                <template slot-scope="scope">
                                    <el-tag type="danger"
                                        v-if="scope.row.warningStatus === '紧急'">紧急</el-tag>
                                    <el-tag type="warning"
                                        v-else-if="scope.row.warningStatus === '一般'">一般</el-tag>
                                    <el-tag type="success" v-else>正常</el-tag>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </section>

                <section class="charts-section">
                    <div class="bar-chart">
                        <el-divider content-position="center">配件用量分析</el-divider>
                        <div id="barChart"
                            style="width: 100%; height: 500px;"></div>
                    </div>
                    <div class="pie-chart">
                        <el-divider content-position="center">配件使用区域分布</el-divider>
                        <div id="pieChart"
                            style="width: 100%; height: 500px;"></div>
                    </div>
                </section>
            </div>
        </div>

        <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    warningItems: [
                        { name: '压缩弹簧', id: 'A001', loss: 240, currentStock: 10, warningStatus: '紧急' },
                        { name: '导向三角', id: 'B002', loss: 100, currentStock: 10, warningStatus: '紧急' },
                        { name: '左侧压纱钩', id: 'C003', loss: 130, currentStock: 10, warningStatus: '紧急' },
                        { name: '大结头探测器', id: 'D004', loss: 83, currentStock: 10, warningStatus: '紧急' },
                        { name: '集圈限位三角', id: 'E005', loss: 95, currentStock: 10, warningStatus: '紧急' },
                        { name: '微动开关', id: 'F006', loss: 123, currentStock: 10, warningStatus: '紧急' }
                    ],
                };
            },
            mounted() {
                this.initBarChart();
                this.initPieChart();
                window.addEventListener('resize', this.handleResize);
            },
            beforeDestroy() {
                window.removeEventListener('resize', this.handleResize);
            },
            methods: {
                initBarChart() {
                    this.barChart = echarts.init(document.getElementById('barChart'));
                    const barOption = {
                        title: {
                            text: '',
                            left: 'center'
                        },
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'shadow'
                            }
                        },
                        xAxis: {
                            type: 'category',
                            data: ['压缩弹簧', '导向三角', '左侧压纱钩', '大结头探测器', '集圈限位三角', '微动开关'],
                            axisLabel: {
                                interval: 0,
                                rotate: 40
                            }
                        },
                        yAxis: {
                            type: 'value'
                        },
                        series: [
                            {
                                data: [96, 27, 69, 177, 102, 85],
                                type: 'bar',
                                barWidth: '50%',
                                itemStyle: {
                                    color: '#FB9755'
                                },
                                label: {
                                    show: true,
                                    position: 'top',
                                }
                            }
                        ]
                    };
                    this.barChart.setOption(barOption);
                },
                initPieChart() {
                    this.pieChart = echarts.init(document.getElementById('pieChart'));
                    const pieOption = {
                        title: {
                            text: '',
                            left: 'center'
                        },
                        tooltip: {
                            trigger: 'item'
                        },
                        legend: {
                            orient: 'horizontal',
                            top: '10%',
                            left: 'center'
                        },
                        color: ['#FB9755', '#FB6C1E', '#F54E10', '#E6430D', '#D7380B', '#C82D09'],
                        series: [
                            {
                                name: '区域分布',
                                type: 'pie',
                                radius: ['40%', '70%'],
                                center: ['50%', '60%'],
                                data: [
                                    { value: 102.97, name: '1#织造横机区域' },
                                    { value: 97, name: '2#织造横机区域' },
                                    { value: 58.6, name: '3#织造横机区域' },
                                    { value: 45.3, name: '4#织造横机区域' },
                                    { value: 89, name: '5#织造横机区域' },
                                    { value: 76, name: '6#织造横机区域' },
                                    { value: 93, name: '7#织造横机区域' },
                                    { value: 42, name: '8#织造横机区域' },
                                    { value: 45.97, name: '9#织造横机区域' },
                                    { value: 107.3, name: '10#织造横机区域' }
                                ],
                                label: {
                                    show: true,
                                    formatter: '{b} ({c}): {d}%',
                                    position: 'top'
                                },
                                emphasis: {
                                    itemStyle: {
                                        shadowBlur: 10,
                                        shadowOffsetX: 0,
                                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                                    }
                                }
                            }
                        ]
                    };
                    this.pieChart.setOption(pieOption);
                },
                handleResize() {
                    if (this.barChart) this.barChart.resize();
                    if (this.pieChart) this.pieChart.resize();
                }
            }
        });
    </script>
    </body>
</html>