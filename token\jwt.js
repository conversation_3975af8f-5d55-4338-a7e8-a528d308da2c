/*
 * @Author: flyknit <EMAIL>
 * @Date: 2023-05-26 10:37:13
 * @LastEditors: flyknit <EMAIL>
 * @LastEditTime: 2023-05-26 16:36:38
 * @FilePath: \electronic-filef:\maintainceMe\token\jwt.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const jwt = require('jsonwebtoken')
// 导入配置文件
const security = require('./tokentime')
// token加密生成
const gentoken = function(userinfo){
	const payload = {user:userinfo}
	const secretkeys = security.secretKey
	const expiresIn = security.expiresIn
	const options = {expiresIn};
	const token = jwt.sign(payload,secretkeys,options)
	return token
}

module.exports = gentoken